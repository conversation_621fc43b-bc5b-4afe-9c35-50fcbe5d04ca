<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- VIEWS -->

        <!-- account.move.line (Journal items) -->
        <record id="view_move_line_form" model="ir.ui.view">
            <field name="name">account.move.line.form</field>
            <field name="model">account.move.line</field>
            <field eval="2" name="priority"/>
            <field name="arch" type="xml">
                <form string="Journal Item" create="false">
                    <sheet>
                        <field name="company_id" invisible="1"/>
                        <field name="parent_state" invisible="1"/>
                        <group>
                            <field name="name"/>
                            <field name="partner_id"
                                domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                readonly="1"/>
                        </group>
                        <notebook colspan="4">
                            <page string="Information" name="information">
                                <group>
                                    <group string="Amount">
                                        <field name="account_id" options="{'no_create': True}" domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id)]" readonly="1"/>
                                        <field name="debit" readonly="1"/>
                                        <field name="credit" readonly="1"/>
                                        <field name="balance" readonly="1"/>
                                        <field name="quantity" readonly="1"/>
                                    </group>
                                    <group string="Dates">
                                        <field name="date" groups="account.group_account_readonly"/>
                                        <field name="date_maturity"/>
                                    </group>

                                    <group string="Taxes" invisible="not tax_line_id and not tax_ids">
                                        <field name="tax_line_id" readonly="1" invisible="not tax_line_id"/>
                                        <field name="tax_ids" widget="many2many_tags" readonly="1" invisible="not tax_ids"/>
                                        <field name="tax_tag_invert" readonly="1" groups="base.group_no_one"/>
                                    </group>
                                    <group string="Matching" invisible="not matched_debit_ids and not matched_credit_ids">
                                        <label for="full_reconcile_id"/>
                                        <div>
                                            <field name="full_reconcile_id" invisible="not full_reconcile_id"/>
                                            <field name="matched_debit_ids" invisible="1"/>
                                            <field name="matched_credit_ids" invisible="1"/>
                                            <button name="open_reconcile_view"
                                                class="oe_link"
                                                type="object"
                                                string="-> View partially reconciled entries"
                                                invisible="full_reconcile_id or not matched_debit_ids and not matched_credit_ids">
                                            </button>
                                        </div>
                                    </group>
                                    <group string="Currency" groups="base.group_multi_currency">
                                        <field name="currency_id" invisible="1"/>
                                        <field name="amount_currency"/>
                                    </group>
                                    <group string="Product" invisible="not product_id">
                                        <field name="product_id" readonly="1"/>
                                    </group>
                                    <group string="Analytic" groups="analytic.group_analytic_accounting">
                                        <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"
                                            readonly="1"/>
                                    </group>
                                </group>
                            </page>
                            <page string="Analytic Lines" name="analytic_lines" groups="analytic.group_analytic_accounting">
                                <field name="date" invisible="1"/>
                                <field name="analytic_line_ids" context="{'list_view_ref':'analytic.view_account_analytic_line_tree', 'default_general_account_id':account_id, 'default_name': name, 'default_date':date, 'amount': (debit or 0.0)-(credit or 0.0)}"/>
                            </page>
                            <page string="Accounting documents" name="page_accounting_documents">
                                <label for="move_id" string="Journal Entry"/>
                                <field name="move_id" readonly="1" nolabel="1"/>
                                <group readonly="1" invisible="not statement_line_id">
                                    <label for="statement_line_id" string="Originator Statement Line"/>
                                    <field name="statement_line_id"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="account_move_line_view_kanban" model="ir.ui.view">
            <field name="name">account.move.line.kanban</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" create="false" group_create="false">
                    <field name="company_currency_id"/>
                    <templates>
                        <t t-name="card">
                            <div class="row mb4">
                                <div class="col-8">
                                    <field name="account_id"/>
                                </div>
                                <strong t-if="record.date_maturity.raw_value" class="col-4 ps-0 text-end">
                                    <i class="fa fa-clock-o" aria-label="Date" role="img" title="Date"/> <field name="date_maturity"/>
                                </strong>
                            </div>
                            <div class="row mb4" style="min-height: 60px;">
                                <em class="col-10">
                                    <field name="name"/>
                                </em>
                                <div class="col-2 text-end">
                                    <field name="partner_id" widget="image" options="{'preview_image': 'avatar_128'}"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <field name="tax_ids" widget="many2many_tags"/>
                                </div>
                                <div class="col-6 text-end">
                                    <t t-if="record.debit.raw_value > 0">
                                        <field name="debit"/><span> (DR)</span>
                                    </t>
                                    <t t-if="record.credit.raw_value > 0">
                                        <field name="credit"/><span> (CR)</span>
                                    </t>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="account_move_line_view_kanban_mobile" model="ir.ui.view">
            <field name="inherit_id" ref="account_move_line_view_kanban"/>
            <field name="mode">primary</field>
            <field name="model">account.move.line</field>
            <field name="name">account.move.line.kanban.mobile</field>
            <field name="arch" type="xml">
                <xpath expr="//kanban[hasclass('o_kanban_mobile')]" position="attributes">
                    <attribute name="create">true</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_move_line_pivot" model="ir.ui.view">
            <field name="name">account.move.line.pivot</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <pivot string="Journal Items" sample="1">
                    <field name="journal_id" type="row"/>
                    <field name="date" type="col"/>
                    <field name="balance" type="measure"/>
                </pivot>
            </field>
        </record>

        <record id="view_move_line_tree" model="ir.ui.view">
            <field name="name">account.move.line.list</field>
            <field name="model">account.move.line</field>
            <field eval="10" name="priority"/>
            <field name="arch" type="xml">
                <list name="move_line_tree" string="Journal Items" create="false" edit="true" expand="context.get('expand', False)" multi_edit="1" sample="1">
                    <field name="move_id" column_invisible="True"/>
                    <field name="invoice_date" string="Invoice Date" optional="hide"/>
                    <field name="date" readonly="1"/>
                    <field name="company_id" column_invisible="True"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hide"/>
                    <field name="journal_id" readonly="1" options='{"no_open":True}' optional="hide"/>
                    <field name="move_name" string="Journal Entry" widget="open_move_widget"/>
                    <field name="account_id" options="{'no_open': True, 'no_create': True}" domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id)]" groups="account.group_account_readonly"/>
                    <field name="partner_id" optional="show" readonly="move_type != 'entry'"/>
                    <field name="ref" optional="hide" readonly="False"/>
                    <field name="product_id" readonly="1" optional="hide"/>
                    <field name="name" optional="show"/>
                    <field name="analytic_distribution"
                           widget="analytic_distribution"
                           groups="analytic.group_analytic_accounting"
                           optional="hide"
                           options="{'product_field': 'product_id', 'account_field': 'account_id', 'force_applicability': 'optional'}"
                    />
                    <field name="tax_ids" widget="many2many_tags" optional="hide" readonly="1"/>
                    <field name="amount_currency" string="In Currency" groups="base.group_multi_currency" optional="hide" readonly="1" invisible="is_same_currency"/>
                    <field name="currency_id" groups="base.group_multi_currency" optional="hide" string="Currency" readonly="1" invisible="is_same_currency"/>
                    <field name="debit" sum="Total Debit" readonly="1"/>
                    <field name="credit" sum="Total Credit" readonly="1"/>
                    <field name="tax_tag_ids" string="Tax Grids" options="{'no_create': True, 'no_create_edit': True}" domain="[('applicability', '=', 'taxes')]" widget="many2many_tags" optional="hide"/>
                    <field name="discount_date" string="Discount Date" optional="hide" />
                    <field name="discount_amount_currency" string="Discount Amount" optional="hide" />
                    <field name="tax_line_id" string="Originator Tax" optional="hide" readonly="1"/>
                    <field name="date_maturity" readonly="1" optional="hide"/>
                    <field name="balance" sum="Total Balance" optional="hide" readonly="1"/>
                    <field name="matching_number" string="Matching" readonly="1" optional="show"/>
                    <field name="amount_residual" sum="Total Residual" string="Residual" optional="hide" readonly="1" invisible="not is_account_reconcile"/>
                    <field name="amount_residual_currency" sum="Total Residual in Currency" string="Residual in Currency" optional="hide" readonly="1" invisible="is_same_currency or not is_account_reconcile" groups="base.group_multi_currency"/>
                    <field name="move_type" column_invisible="True"/>
                    <field name="parent_state" column_invisible="True"/>
                    <field name="account_type" column_invisible="True"/>
                    <field name="statement_line_id" column_invisible="True"/>
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="is_same_currency" column_invisible="True"/>
                    <field name="is_account_reconcile" column_invisible="True"/>
                    <field name="sequence" column_invisible="True"/>
                    <groupby name="partner_id">
                        <button name="edit" type="edit" icon="fa-edit" title="Edit"/>
                    </groupby>
                </list>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_sales_purchases" model="ir.ui.view">
            <field name="name">account.move.line.list.grouped.sales.purchase</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="tax_tag_ids" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_bank_cash" model="ir.ui.view">
            <field name="name">account.move.line.list.grouped.bank.cash</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_misc" model="ir.ui.view">
            <field name="name">account.move.line.list.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="date" position="attributes"><attribute name="optional">hide</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tree_grouped_general" model="ir.ui.view">
            <field name="name">account.move.line.list.grouped.misc</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="account_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="balance" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>


        <record id="view_move_line_tree_grouped_partner" model="ir.ui.view">
            <field name="name">account.move.line.list.grouped.partner</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes"><attribute name="optional">hide</attribute></field>
                <field name="date_maturity" position="attributes"><attribute name="optional">show</attribute></field>
                <field name="balance" position="attributes"><attribute name="optional">show</attribute></field>
            </field>
        </record>

        <record id="view_move_line_tax_audit_tree" model="ir.ui.view">
            <field name="name">account.move.line.tax.audit.list</field>
            <field name="model">account.move.line</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <field name="matching_number" position="replace">
                    <field name="tax_line_id" string="Tax"/>
                    <field name="tax_base_amount" sum="Total Base Amount"/>
                </field>
                <field name="analytic_distribution" position="attributes">
                    <attribute name="optional">hide</attribute>
                </field>
                <field name="debit" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
                <field name="credit" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
                <field name="journal_id" position="attributes">
                    <attribute name="optional">show</attribute>
                </field>
            </field>
        </record>

        <record id="account_move_line_graph_date" model="ir.ui.view">
            <field name="name">account.move.line.graph</field>
            <field name="model">account.move.line</field>
            <field name="arch" type="xml">
                <graph string="Account Statistics" sample="1">
                    <field name="date"/>
                    <field name="balance" operator="+" type='measure'/>
                </graph>
            </field>
        </record>

        <record id="view_account_move_line_filter" model="ir.ui.view">
            <field name="name">account.move.line.search</field>
            <field name="model">account.move.line</field>
            <field eval="16" name="priority"/>
            <field name="arch" type="xml">
                <search string="Search Journal Items">
                    <field name="name" string="Journal Item" filter_domain="[
                        '|', '|', '|',
                        ('name', 'ilike', self), ('ref', 'ilike', self), ('account_id', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="invoice_date"/>
                    <field name="date"/>
                    <field name="date_maturity" string="Due Date"/>
                    <field name="discount_date" string="Discount Date"/>
                    <field name="balance" string="Amount" filter_domain="['|', ('credit', '=', raw_value), ('debit', '=', raw_value)]"/>
                    <field name="account_id"/>
                    <field name="account_type"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="journal_group_id"/>
                    <field name="product_id"/>
                    <field name="product_category_id"/>
                    <field name="move_id" string="Journal Entry" filter_domain="[
                        '|', ('move_id.name', 'ilike', self), ('move_id.ref', 'ilike', self)]"/>
                    <field name="tax_ids" />
                    <field name="tax_line_id" string="Originator Tax"/>
                    <field name="reconcile_model_id"/>
                    <separator/>
                    <filter string="Unposted" name="unposted" domain="[('parent_state', '=', 'draft')]" help="Unposted Journal Items"/>
                    <filter string="Posted" name="posted" domain="[('parent_state', '=', 'posted')]" help="Posted Journal Items"/>
                    <separator groups="account.group_account_secured,base.group_no_one"/>
                    <filter name="not_secured" string="Not Secured" domain="[('move_id.secured', '=', False), ('move_id.state', '=', 'posted')]"
                            groups="account.group_account_secured,base.group_no_one"/>
                    <separator/>
                    <filter string="To check" name="to_check" domain="[('move_id.checked', '=', False), ('parent_state', '!=', 'draft')]"/>
                    <separator/>
                    <filter string="Unreconciled"
                            domain="[('balance', '!=', 0), ('account_id.reconcile', '=', True), '|', ('matching_number', '=', False), ('matching_number', '=like', 'P%')]"
                            name="reconcilable_account"
                            help="Journal items where the account allows reconciliation no matter the residual amount"
                    />
                    <filter string="With residual" domain="[('amount_residual', '!=', 0), ('account_id.reconcile', '=', True)]" help="Journal items where matching number isn't set" name="unreconciled"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bank" domain="[('journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cash" domain="[('journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Credit" name="credit" domain="[('journal_id.type', '=', 'credit')]" context="{'default_journal_type': 'credit'}"/>
                    <filter string="Miscellaneous" domain="[('journal_id.type', '=', 'general')]" name="misc_filter" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Payable" domain="[('account_id.account_type', '=', 'liability_payable'), ('account_id.non_trade', '=', False)]" help="From Trade Payable accounts" name="trade_payable"/>
                    <filter string="Receivable" domain="[('account_id.account_type', '=', 'asset_receivable'), ('account_id.non_trade', '=', False)]" help="From Trade Receivable accounts" name="trade_receivable"/>
                    <filter string="Non Trade Payable" domain="[('account_id.account_type', '=', 'liability_payable'), ('account_id.non_trade', '=', True)]" help="From Non Trade Receivable accounts" name="non_trade_payable" invisible="1"/>
                    <filter string="Non Trade Receivable" domain="[('account_id.account_type', '=', 'asset_receivable'), ('account_id.non_trade', '=', True)]" help="From Non Trade Receivable accounts" name="non_trade_receivable" invisible="1"/>
                    <filter string="P&amp;L Accounts" domain="[('account_id.internal_group', 'in', ('income', 'expense'))]" help="From P&amp;L accounts" name="pl_accounts"/>
                    <filter string="No Bank Transaction" domain="[('statement_line_id', '=', False), ('payment_id', '=', False)]" name="no_st_line_id" invisible="1"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <filter string="Invoice Date" name="invoice_date" date="invoice_date"/>
                    <separator/>
                    <filter string="Report Dates" name="date_between" domain="[('date', '&gt;=', context.get('date_from')), ('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <filter string="Report Dates" name="date_before" domain="[('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <separator/>
                    <filter string="Analytic Accounts" name="analytic_accounts" domain="[('analytic_distribution', 'in', context.get('analytic_ids'))]" invisible="1"/>
                    <group expand="0" string="Group By">
                        <filter string="Journal Entry" name="group_by_move" domain="[]" context="{'group_by': 'move_name'}"/>
                        <filter string="Account" name="group_by_account" domain="[]" context="{'group_by': 'account_id'}"/>
                        <filter string="Partner" name="group_by_partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Date" name="groupby_date" domain="[]" context="{'group_by': 'date'}"/>
                        <filter string="Invoice Date" name="groupby_invoice_date" domain="[]" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Taxes" name="group_by_taxes" domain="[]" context="{'group_by': 'tax_ids'}"/>
                        <filter string="Tax Grid" name="group_by_tax_tags" domain="[]" context="{'group_by': 'tax_tag_ids'}"/>
                        <filter string="Matching" name="group_by_matching" domain="[]" context="{'group_by': 'matching_number'}"/>
                    </group>
                    <searchpanel class="account_root w-auto">
                        <field name="account_root_id" icon="fa-filter" groupby="account_id" limit="0"/>
                    </searchpanel>
                </search>
            </field>
        </record>

        <!-- account.move.line (Payment Items) -->

        <record id="view_move_line_payment_tree" model="ir.ui.view">
            <field name="name">account.move.line.payment.list</field>
            <field name="model">account.move.line</field>
            <field eval="200" name="priority"/>
            <field name="arch" type="xml">
                <list string="Payment Items" create="false" edit="true" expand="context.get('expand', False)" multi_edit="1" sample="1" default_order="payment_date asc, id asc">
                    <header>
                        <button name="action_payment_items_register_payment" type="object" string="Pay" groups="account.group_account_user"/>
                    </header>
                    <field name="move_id" column_invisible="True"/>
                    <field name="invoice_date" string="Bill Date"/>
                    <field name="date" readonly="1" optional="hide"/>
                    <field name="date_maturity" string="Invoice Due Date" readonly="1" optional="hide"/>
                    <field name="discount_date" string="Discount Date" optional="hide"/>
                    <field name="payment_date" readonly="1"/>
                    <field name="company_id" column_invisible="True"/>
                    <field name="company_id" groups="base.group_multi_company" readonly="1" optional="hide"/>
                    <field name="journal_id" readonly="1" options='{"no_open":True}' optional="hide"/>
                    <field name="move_name" string="Journal Entry" widget="open_move_widget"/>
                    <field name="partner_id" optional="show" readonly="move_type != 'entry'"/>
                    <field name="ref" readonly="False"/>
                    <field name="name" optional="show"/>
                    <field name="discount_amount_currency" string="Discount Amount" optional="show" invisible="not discount_amount_currency"/>
                    <field name="amount_residual" sum="Total Residual" string="Residual" readonly="1" invisible="not is_account_reconcile"/>
                    <field name="amount_residual_currency" string="Residual in Currency" optional="hide" readonly="1" invisible="is_same_currency or not is_account_reconcile" groups="base.group_multi_currency"/>
                    <field name="currency_id" groups="base.group_multi_currency" optional="hide" string="Currency" readonly="1" invisible="is_same_currency"/>
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="move_type" column_invisible="True"/>
                    <field name="is_same_currency" column_invisible="True"/>
                    <field name="is_account_reconcile" column_invisible="True"/>
                    <field name="currency_id" groups="!base.group_multi_currency" column_invisible="True"/>
                    <groupby name="partner_id">
                        <button name="edit" type="edit" icon="fa-edit" title="Edit"/>
                    </groupby>
                </list>
            </field>
        </record>

        <record id="view_account_move_line_payment_filter" model="ir.ui.view">
            <field name="name">account.move.line.payment.search</field>
            <field name="model">account.move.line</field>
            <field eval="200" name="priority"/>
            <field name="arch" type="xml">
                <search string="Search Journal Items">
                    <field name="name" string="Journal Item" filter_domain="[
                        '|', '|', '|',
                        ('name', 'ilike', self), ('ref', 'ilike', self), ('account_id', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="name"/>
                    <field name="move_id"/>
                    <field name="ref"/>
                    <field name="payment_date"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <field name="currency_id" groups="base.group_multi_currency" invisible="1"/>
                    <field name="company_currency_id" invisible="1"/>
                    <separator/>
                    <filter string="Posted" name="posted" domain="[('parent_state', '=', 'posted')]" help="Posted Journal Items" invisible="1"/>
                    <separator/>
                    <filter string="Invoices" name="invoices" domain="[('amount_residual', '&gt;', '0')]" invisible="context.get('journal_type') != 'sale'"/>
                    <filter string="Credit Notes" name="credit_notes" domain="[('amount_residual', '&lt;', '0')]" invisible="context.get('journal_type') != 'sale'"/>
                    <filter string="Bills" name="bills" domain="[('amount_residual', '&lt;', '0')]" invisible="context.get('journal_type') != 'purchase'"/>
                    <filter string="Refunds" name="refunds" domain="[('amount_residual', '&gt;', '0')]" invisible="context.get('journal_type') != 'purchase'"/>
                    <separator/>
                    <filter string="Invoice Date" name="invoice_date" date="invoice_date"/>
                    <filter string="Next Payment Date" name="payment_date" date="payment_date"/>
                    <filter string="Overdue" name="late" domain="[('date_maturity', '&lt;', time.strftime('%Y-%m-%d'))]" help="Overdue payments, due date passed"/>
                    <filter string="Early Discount" name="early_discount" domain="[('discount_date', '!=', False), ('discount_date', '&gt;', time.strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <filter string="Report Dates" name="date_between" domain="[('date', '&gt;=', context.get('date_from')), ('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <filter string="Report Dates" name="date_before" domain="[('date', '&lt;=', context.get('date_to'))]" invisible="1"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Currency" name="group_by_currencies" domain="[]" context="{'group_by': 'currency_id'}"/>
                        <filter string="Partner" name="group_by_partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Invoice Date" name="groupby_invoice_date" domain="[]" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Next Payment Date" name="groupby_payment_date" domain="[]" context="{'group_by': 'payment_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="view_move_tree" model="ir.ui.view">
            <field name="name">account.move.list</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <list string="Journal Entries" sample="1" decoration-info="state == 'draft'" expand="context.get('expand', False)"  multi_edit="1">
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="made_sequence_gap" column_invisible="True"/>
                    <field name="invoice_date" string="Invoice Date" optional="hide" readonly="state != 'draft'"/>
                    <field name="date" readonly="state in ['cancel', 'posted']"/>
                    <field name="name" decoration-danger="made_sequence_gap and state == 'posted'" widget="char_with_placeholder_field" placeholder="/"/>
                    <field name="partner_id" optional="show" readonly="state != 'draft'"/>
                    <field name="ref" optional="show"/>
                    <field name="journal_id"/>
                    <field name="company_id" groups="base.group_multi_company" optional="show"/>
                    <field name="amount_total_signed" sum="Total Amount" string="Total" decoration-bf="1"/>
                    <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-success="state == 'posted'"/>
                    <field name="currency_id" column_invisible="True" readonly="state in ['cancel', 'posted']"/>
                    <field name="checked" optional="hide" widget="boolean_toggle"/>
                    <field name="activity_ids" widget="list_activity" optional="hide"/>
                </list>
            </field>
        </record>

        <record id="view_move_tree_multi_edit" model="ir.ui.view">
            <field name="name">account.move.list.multi.edit</field>
            <field name="model">account.move</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_move_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="multi_edit">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_invoice_tree" model="ir.ui.view">
            <field name="name">account.invoice.list</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <list string="Invoices"
                      js_class="account_tree"
                      decoration-info="state == 'draft'"
                      decoration-muted="state == 'cancel'"
                      expand="context.get('expand', False)"
                      sample="1">
                    <header>
                        <button name="action_force_register_payment" type="object" string="Pay"
                            groups="account.group_account_invoice"
                            invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt', 'in_invoice', 'in_refund','in_receipt')"/>
                    </header>
                    <field name="made_sequence_gap" column_invisible="True"/>
                    <field name="duplicated_ref_ids" column_invisible="True"/>  <!-- Needed for custome view account_tree-->
                    <field name="name" decoration-bf="1" decoration-danger="made_sequence_gap and state == 'posted'" widget="char_with_placeholder_field" placeholder="/"/>
                    <field name="invoice_partner_display_name" column_invisible="context.get('default_move_type') not in ('in_invoice', 'in_refund', 'in_receipt')" groups="base.group_user" string="Vendor" />
                    <field name="invoice_partner_display_name" column_invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt')" groups="base.group_user" string="Customer" />
                    <field name="invoice_date" optional="show" column_invisible="context.get('default_move_type') not in ('in_invoice', 'in_refund', 'in_receipt')" readonly="state != 'draft'" string="Bill Date" decoration-warning="abnormal_date_warning"/>
                    <field name="invoice_date" optional="show" column_invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt')" readonly="state != 'draft'" string="Invoice Date" decoration-warning="abnormal_date_warning"/>
                    <field name="date" optional="hide" string="Accounting Date" readonly="state in ['cancel', 'posted']"/>
                    <field name="invoice_date_due" widget="remaining_days" optional="show" invisible="payment_state in ('paid', 'in_payment', 'reversed') or state == 'cancel'"/>
                    <field name="invoice_origin" optional="hide" string="Source Document"/>
                    <field name="payment_reference" optional="hide" column_invisible="context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')"/>
                    <field name="ref" optional="hide"/>
                    <field name="invoice_user_id" optional="hide" column_invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt')" string="Salesperson" widget="many2one_avatar_user"/>
                    <field name="activity_ids" widget="list_activity" optional="hide"/>
                    <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}" optional="hide"/>
                    <field name="company_id" groups="!base.group_multi_company" column_invisible="True"/>
                    <field name="amount_untaxed_in_currency_signed" string="Tax Excluded" sum="Total" optional="show"/>
                    <field name="amount_tax_signed" string="Tax" sum="Total" optional="hide"/>
                    <field name="amount_total_in_currency_signed" string="Total" sum="Total" optional="show" decoration-bf="1" decoration-warning="abnormal_amount_warning" groups="base.group_multi_currency"/>
                    <field name="amount_total_in_currency_signed" string="Total" sum="Total" optional="hide" decoration-bf="1" decoration-warning="abnormal_amount_warning" groups="!base.group_multi_currency"/>
                    <field name="amount_residual_signed" string="Amount Due" sum="Amount Due" optional="hide"/>
                    <field name="currency_id" optional="hide" readonly="state in ['cancel', 'posted']"/>
                    <field name="company_currency_id" column_invisible="True"/>
                    <field name="checked" optional="hide" widget="boolean_toggle"/>
                    <field name="status_in_payment"
                           string="Status"
                           widget="badge"
                           decoration-info="state == 'draft'"
                           decoration-danger="payment_state == 'not_paid' and invoice_date_due &lt; context_today().strftime('%Y-%m-%d')"
                           decoration-warning="payment_state in ('partial', 'in_payment')"
                           decoration-success="payment_state in ('paid', 'reversed')"
                           invisible="payment_state == 'invoicing_legacy' or move_type == 'entry'"
                           optional="show"
                    />
                    <field name="move_sent_values"
                           string="Sent"
                           widget="badge"
                           decoration-success="move_sent_values == 'sent'"
                           decoration-danger="move_sent_values == 'not_sent'"
                           column_invisible="context.get('default_move_type') not in ('out_invoice', 'out_refund', 'out_receipt')"
                           optional="hide"
                    />
                    <field name="move_type" column_invisible="context.get('default_move_type', True)"/>
                    <field name="abnormal_amount_warning" column_invisible="1"/>
                    <field name="abnormal_date_warning" column_invisible="1"/>
                  </list>
            </field>
        </record>

        <record id="view_duplicated_moves_tree_js" model="ir.ui.view">
            <field name="name">account.duplicated.moves.list.js</field>
            <field name="model">account.move</field>
            <field name="mode">primary</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="priority">999</field>
            <field name="arch" type="xml">
                <xpath expr="//list" position="attributes">
                    <attribute name="js_class">account_x2many_list</attribute>
                    <attribute name="create">false</attribute>
                </xpath>
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="column_invisible">True</attribute>
                </xpath>
                <xpath expr="//field[@name='name']" position="after">
                    <!-- For draft moves, name only displays '/'. Since there may be moves of differenty types,
                         it is better to use display_name indicating what move it refers to when in draft-->
                    <field name="display_name" decoration-bf="1" string="Number" context="{'name_with_move_type': True}" widget="char_with_placeholder_field" placeholder="/"/>
                </xpath>
            </field>
        </record>

        <record id="view_out_invoice_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//list/header" position="inside">
                    <button name="action_send_and_print" type="object" string="Send"/>
                </xpath>
                <field name="currency_id" position="attributes">
                    <attribute name="string">Invoice Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_out_credit_note_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//list/header" position="inside">
                    <button name="action_send_and_print" type="object" string="Send"/>
                </xpath>
                <field name="currency_id" position="attributes">
                    <attribute name="string">Credit Note Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='ref']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_in_invoice_bill_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Bill Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_refund_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Refund Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_in_invoice_receipt_tree" model="ir.ui.view">
            <field name="name">account.out.invoice.list</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_in_invoice_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="currency_id" position="attributes">
                    <attribute name="string">Receipt Currency</attribute>
                </field>
            </field>
        </record>

        <record id="view_account_move_kanban" model="ir.ui.view">
            <field name="name">account.move.kanban</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" sample="1" js_class="account_documents_kanban">
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="card">
                            <div class="row mb4">
                                <div class="col-6">
                                    <field class="fw-bold" name="partner_id" invisible="not partner_id" readonly="state != 'draft'" />
                                    <field class="fw-bold" name="journal_id" invisible="partner_id" />
                                </div>
                                <div class="col-6 text-end">
                                    <i class="fa fa-clock-o fw-bold" aria-label="Date" role="img" title="Date"/> <field class="fw-bold" name="date"/>
                                </div>
                            </div>
                            <field name="ref"/>
                            <div class="row">
                                <div class="col-6">
                                    <field name="amount_total_in_currency_signed" widget='monetary'/>
                                </div>
                                <div class="col-6">
                                    <field class="float-end" name="state" widget="label_selection" options="{'classes': {'draft': 'default', 'posted': 'success'}}"/>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <form string="Account Entry" js_class="account_move_form">
                    <header>
                        <!-- Post -->
                        <button name="action_post" string="Post" class="oe_highlight"
                                type="object" groups="account.group_account_invoice" data-hotkey="q"
                                context="{'validate_analytic': True, 'disable_abnormal_invoice_detection': False}"
                                invisible="hide_post_button or move_type != 'entry'"/>
                        <button name="action_post" string="Confirm" class="oe_highlight"
                                type="object" groups="account.group_account_invoice" data-hotkey="q"
                                context="{'validate_analytic': True, 'disable_abnormal_invoice_detection': False}"
                                invisible="hide_post_button or move_type == 'entry' or display_inactive_currency_warning"/>
                        <!-- Send (only invoices) -->
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send"
                                invisible="state != 'posted' or is_being_sent or invoice_pdf_report_id or move_type not in ('out_invoice', 'out_refund')"
                                class="oe_highlight"
                                data-hotkey="y"/>
                        <button name="action_invoice_sent"
                                type="object"
                                string="Send"
                                invisible="state != 'posted' or not is_being_sent and not invoice_pdf_report_id or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')"
                                data-hotkey="y"/>
                        <button name="action_print_pdf"
                                type="object"
                                class="oe_highlight"
                                invisible="state != 'posted' or is_being_sent or invoice_pdf_report_id or move_type == 'entry'"
                                string="Print"/>
                        <button name="action_print_pdf"
                                type="object"
                                invisible="state != 'posted' or (not is_being_sent and not invoice_pdf_report_id)"
                                string="Print"/>
                        <!-- Register Payment (only invoices / receipts) -->
                        <button name="action_register_payment" id="account_invoice_payment_btn"
                                type="object" class="oe_highlight"
                                invisible="(
                                    state != 'posted'
                                    or payment_state not in ('not_paid', 'partial', 'in_payment')
                                    or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')
                                    or invoice_has_outstanding
                                )"
                                context="{'dont_redirect_to_payments': True, 'display_account_trust': True}"
                                string="Pay" data-hotkey="g"
                                groups="account.group_account_invoice"/>
                        <!-- Register Payment (only invoices / receipts, with outstanding payments) -->
                        <button name="action_register_payment" id="account_invoice_payment_secondary_btn"
                                type="object"
                                invisible="(
                                    state != 'posted'
                                    or payment_state not in ('not_paid', 'partial', 'in_payment')
                                    or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')
                                    or not invoice_has_outstanding
                                )"
                                context="{'dont_redirect_to_payments': True, 'display_account_trust': True}"
                                string="Pay" data-hotkey="g"
                                groups="account.group_account_invoice"/>
                        <!-- Preview (only customer invoices) -->
                        <button name="preview_invoice" type="object" string="Preview" data-hotkey="o"
                                title="Preview invoice"
                                invisible="move_type not in ('out_invoice', 'out_refund') or state in ('draft', 'cancel')"/>
                        <!-- Reverse -->
                        <button name="%(action_view_account_move_reversal)d" string="Reverse Entry"
                                type="action" groups="account.group_account_invoice" data-hotkey="z"
                                invisible="move_type != 'entry' or state != 'posted' or payment_state == 'reversed'"/>
                        <button name="action_reverse" string='Credit Note'
                                type='object' groups="account.group_account_invoice"
                                invisible="move_type not in ('out_invoice', 'in_invoice') or state != 'posted'"
                                data-hotkey="shift+n"/>
                        <!-- Cancel -->
                        <button name="button_cancel" string="Cancel Entry" type="object" groups="account.group_account_invoice" data-hotkey="x"
                                invisible="not id or state != 'draft' or move_type != 'entry'"/>
                        <button name="button_cancel" string="Cancel" type="object" groups="account.group_account_invoice" data-hotkey="x"
                                invisible="not id or state != 'draft' or move_type == 'entry'"/>
                        <button name="button_draft" string="Reset to Draft" type="object" groups="account.group_account_invoice"
                                invisible="not show_reset_to_draft_button" data-hotkey="r" />
                        <button name="button_hash" string="Lock" type="object" groups="account.group_account_invoice"
                                invisible="not restrict_mode_hash_table or inalterable_hash or state != 'posted'"/>
                        <button string="Request Cancel"
                                name="button_request_cancel"
                                type="object"
                                groups="account.group_account_invoice"
                                invisible="state != 'posted' or show_reset_to_draft_button or not need_cancel_request"
                                data-hotkey="w"/>

                        <!-- Set as Checked -->
                        <button name="button_set_checked" string="Set as Checked" type="object" groups="account.group_account_invoice"
                                invisible="checked or state == 'draft'" data-hotkey="k" />
                        <field name="state" widget="statusbar" statusbar_visible="draft,posted"
                               groups="!account.group_account_secured"/>
                        <field name="state" widget="account_move_statusbar_secured" statusbar_visible="draft,posted"
                               groups="account.group_account_secured"/>
                    </header>
                    <div class="alert alert-warning w-100 d-flex align-items-center gap-1" invisible="not duplicated_ref_ids"  role="alert">
                        <span>Warning: this document might be a duplicate of</span>
                        <field name="duplicated_ref_ids" widget="x2many_buttons" string="Duplicated Documents" context="{'name_as_amount_total': True}"/>
                    </div>
                    <div class="alert alert-info" role="alert" invisible="not is_being_sent">
                        This invoice is being sent in the background.
                    </div>
                    <!-- Invoice outstanding credits -->
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-warning" role="alert"
                         invisible="state != 'draft' or not tax_lock_date_message">
                        <field name="tax_lock_date_message" nolabel="1"/>
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info" role="alert"
                         invisible="move_type not in ('out_invoice', 'out_receipt') or not invoice_has_outstanding or payment_state not in ('not_paid', 'partial')">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding credits</a></bold> listed below for this customer.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info" role="alert"
                         invisible="move_type not in ('in_invoice', 'in_receipt') or not invoice_has_outstanding or payment_state not in ('not_paid', 'partial')">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> listed below for this vendor.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info" role="alert"
                         invisible="move_type != 'out_refund' or not invoice_has_outstanding or payment_state not in ('not_paid', 'partial')">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding debits</a></bold> listed below for this customer.
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-info" role="alert"
                         invisible="move_type != 'in_refund' or not invoice_has_outstanding or payment_state not in ('not_paid', 'partial')">
                        You have <bold><a class="alert-link" href="#outstanding" role="button">outstanding credits</a></bold> listed below for this vendor.
                    </div>
                    <div class="alert alert-info" role="alert"
                         invisible="state != 'draft' or auto_post != 'at_date'">
                        This move is configured to be posted automatically at the accounting date: <field name="date" readonly="1"/>.
                    </div>
                    <div class="alert alert-info" role="alert"
                         invisible="state != 'draft' or auto_post == 'no' or auto_post == 'at_date'">
                         <field name="auto_post" readonly="1"/> auto-posting enabled. Next accounting date: <field name="date" readonly="1"/>.<span invisible="not auto_post_until"> The recurrence will end on <field name="auto_post_until" readonly="1"/> (included).</span>
                    </div>
                    <div groups="account.group_account_invoice,account.group_account_readonly"
                         class="alert alert-warning" role="alert"
                         invisible="partner_credit_warning == ''">
                        <field name="partner_credit_warning"/>
                    </div>
                    <!-- Currency consistency -->
                    <div class="alert alert-warning" role="alert"
                         invisible="not display_inactive_currency_warning or move_type not in ('in_invoice', 'in_refund', 'in_receipt')">
                        In order to validate this bill, you must <button class="oe_link" type="object" name="action_activate_currency" style="padding: 0; vertical-align: baseline;">activate the currency of the bill</button>. The journal entries need to be computed by Odoo before being posted in your company's currency.
                    </div>
                    <div class="alert alert-warning" role="alert"
                         invisible="not display_inactive_currency_warning or move_type not in ('out_invoice', 'out_refund', 'out_receipt')">
                        In order to validate this invoice, you must <button class="oe_link" type="object" name="action_activate_currency" style="padding: 0; vertical-align: baseline;">activate the currency of the invoice</button>. The journal entries need to be computed by Odoo before being posted in your company's currency.
                    </div>
                    <div class="alert alert-warning" role="alert"
                         invisible="not abnormal_amount_warning">
                        <field name="abnormal_amount_warning"/>
                    </div>
                    <div class="alert alert-warning" role="alert"
                         invisible="not abnormal_date_warning">
                        <field name="abnormal_date_warning"/>
                    </div>
                    <sheet>
                        <div name="button_box" class="oe_button_box">
                            <button name="action_open_business_doc"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    invisible="move_type != 'entry' or not id or not origin_payment_id">
                                    <div class="o_stat_info">
                                        <span class="o_stat_text">1 Payment</span>
                                    </div>
                            </button>
                            <button name="open_payments"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    invisible="not matched_payment_ids">
                                    <field name="payment_count" string="Payments" widget="statinfo"/>
                            </button>
                            <button name="open_reconcile_view"
                                    class="oe_stat_button"
                                    icon="fa-bars"
                                    type="object"
                                    invisible="move_type != 'entry' or not id or not has_reconciled_entries">
                                    <div class="o_stat_info">
                                        <span class="o_stat_text">Reconciled Items</span>
                                    </div>
                            </button>
                            <button name="open_created_caba_entries"
                                    class="oe_stat_button"
                                    icon="fa-usd"
                                    type="object"
                                    invisible="not tax_cash_basis_created_move_ids">
                                    <div class="o_stat_info">
                                        <span class="o_stat_text">Cash Basis Entries</span>
                                    </div>
                            </button>
                        </div>

                        <!-- Payment status for invoices / receipts -->
                        <widget name="web_ribbon" title="Paid"
                                invisible="payment_state != 'paid' or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"/>
                        <widget name="web_ribbon" title="In Payment"
                                invisible="payment_state != 'in_payment' or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"/>
                        <widget name="web_ribbon" title="Partial"
                                invisible="payment_state != 'partial' or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"/>
                        <widget name="web_ribbon" title="Reversed"
                                bg_color="text-bg-danger"
                                invisible="payment_state != 'reversed'"/>
                        <widget name="web_ribbon" title="Blocked"
                                bg_color="text-bg-danger"
                                invisible="payment_state != 'blocked' or move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"/>
                         <widget name="web_ribbon" text="Invoicing App Legacy"
                                bg_color="text-bg-info"
                                invisible="payment_state != 'invoicing_legacy'"
                                tooltip="This entry has been generated through the Invoicing app, before installing Accounting. It has been disabled by the 'Invoicing Switch Threshold Date' setting so that it does not impact your accounting."/>

                        <!-- Invisible fields -->
                        <field name="id" invisible="1"/>
                        <field name="state" invisible="1"/>
                        <field name="company_id" invisible="1"/>
                        <field name="journal_id" invisible="1"/>
                        <field name="show_name_warning" invisible="1"/>
                        <field name="posted_before" invisible="1"/>
                        <!--test-tags .test_01_account_tour -->
                        <field name="move_type" invisible="1" force_save="1"/>
                        <field name="payment_state" invisible="1" force_save="1"/>
                        <field name="invoice_filter_type_domain" invisible="1"/>
                        <field name="suitable_journal_ids" invisible="1"/>
                        <field name="currency_id" invisible="1" readonly="state in ['cancel', 'posted']"/>
                        <field name="company_currency_id" invisible="1"/>
                        <field name="commercial_partner_id" invisible="1"/>
                        <field name="bank_partner_id" invisible="1"/>
                        <field name="display_qr_code" invisible="1"/>
                        <field name="show_reset_to_draft_button" invisible="1"/>
                        <field name="expected_currency_rate" invisible="1"/>

                        <field name="invoice_has_outstanding" invisible="1"/>
                        <field name="is_move_sent" invisible="1"/>
                        <field name="invoice_pdf_report_id" invisible="1"/>
                        <field name="need_cancel_request" invisible="1"/>
                        <field name="has_reconciled_entries" invisible="1"/>
                        <field name="restrict_mode_hash_table" invisible="1"/>
                        <field name="inalterable_hash" invisible="1"/>
                        <field name="country_code" invisible="1"/>
                        <field name="display_inactive_currency_warning" invisible="1"/>
                        <field name="statement_line_id" invisible="1"/>
                        <field name="statement_id" invisible="1"/>
                        <field name="origin_payment_id" invisible="1"/>
                        <field name="tax_country_id" invisible="1"/>
                        <field name="tax_calculation_rounding_method" invisible="1"/>
                        <field name="tax_cash_basis_created_move_ids" invisible="1"/>
                        <field name="quick_edit_mode" invisible="1"/>
                        <field name="hide_post_button" invisible="1"/>
                        <field name="quick_encoding_vals" invisible="1"/>
                        <field name="show_delivery_date" invisible="1"/>
                        <field name="is_being_sent" invisible="1"/>
                        <field name="show_update_fpos" invisible="1"/>

                        <div class="oe_title">
                            <span class="o_form_label"><field name="move_type" readonly="1" invisible="move_type == 'entry'" nolabel="1"/></span>

                            <div class="text-warning" invisible="not show_name_warning">The current highest number is <field class="oe_inline" name="highest_name"/>. You might want to put a higher number here.</div>

                            <h1>
                                <field name="name" invisible="not (name or name_placeholder or quick_edit_mode)" readonly="state != 'draft'" options="{'placeholder_field': 'name_placeholder'}"/>

                                <span invisible="name or name_placeholder or quick_edit_mode">Draft</span>
                            </h1>
                        </div>
                        <group>
                            <group id="header_left_group">

                                <label for="partner_id" string="Customer" style="font-weight:bold;"
                                       invisible="move_type not in ('out_invoice', 'out_refund', 'out_receipt')"/>
                                <label for="partner_id" string="Vendor" style="font-weight:bold;"
                                       invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')"/>
                                <div class="o_col" invisible="move_type not in ('out_invoice', 'out_refund', 'out_receipt', 'in_invoice', 'in_refund', 'in_receipt')">
                                    <field name="partner_id" widget="res_partner_many2one" nolabel="1"
                                           context="{
                                            'res_partner_search_mode': (context.get('default_move_type', 'entry') in ('out_invoice', 'out_refund', 'out_receipt') and 'customer') or (context.get('default_move_type', 'entry') in ('in_invoice', 'in_refund', 'in_receipt') and 'supplier') or False,
                                            'show_address': 1, 'default_is_company': True, 'show_vat': True}"
                                           domain="[('company_id', 'in', (False, company_id))]"
                                           options='{"no_quick_create": True}'
                                           default_focus="1"
                                           invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"
                                           placeholder="Search a name or Tax ID..."
                                           readonly="state != 'draft'"/>
                                    <button name="action_update_fpos_values" type="object"
                                            string="Update Taxes and Accounts"
                                            help="Recompute all taxes and accounts based on this fiscal position"
                                            class="btn-link mb-1 px-0" icon="fa-refresh"
                                            confirm="This will update all taxes and accounts based on the currently selected fiscal position."
                                            invisible="not show_update_fpos or state in ['cancel', 'posted']"/>
                                </div>

                                <field name="partner_shipping_id"
                                       groups="account.group_delivery_invoice_address"
                                       invisible="move_type not in ('out_invoice', 'out_refund', 'out_receipt')"
                                       readonly="state != 'draft'"/>
                                <field name="quick_edit_total_amount" class="w-50"
                                       invisible="move_type == 'entry' or not quick_edit_mode"
                                       readonly="state != 'draft'"/>
                                <label for="ref" string="Bill Reference"
                                       invisible="move_type not in ('in_invoice', 'in_receipt', 'in_refund')" />
                                <field name="ref" default_focus="1" nolabel="1" invisible="move_type not in ('in_invoice', 'in_receipt', 'in_refund')"/>
                                <field name="ref" default_focus="1" invisible="move_type in ('in_invoice', 'in_receipt', 'in_refund', 'out_invoice', 'out_refund')"/>
                                <field name="tax_cash_basis_origin_move_id" invisible="not tax_cash_basis_origin_move_id"/>
                                <label name="invoice_vendor_bill_id_label" for="invoice_vendor_bill_id" string="Auto-Complete" class="oe_edit_only"
                                       invisible="state != 'draft' or move_type != 'in_invoice'"/>
                                <field name="invoice_vendor_bill_id" nolabel="1" class="oe_edit_only"
                                       invisible="state != 'draft' or move_type != 'in_invoice'"
                                       domain="[('company_id', '=', company_id), ('partner_id','child_of', [partner_id]), ('move_type','=','in_invoice')]"
                                       placeholder="Select an old vendor bill"
                                       options="{'no_create': True}" context="{'show_total_amount': True}"/>
                            </group>
                            <group id="header_right_group">
                                <label for="invoice_date" string="Invoice Date" style="font-weight:bold;"
                                       invisible="move_type not in ('out_invoice', 'out_refund', 'out_receipt')"/>
                                <field name="invoice_date" nolabel="1" options="{'warn_future': true}"
                                       invisible="move_type not in ('out_invoice', 'out_refund', 'out_receipt')"
                                       readonly="state != 'draft'" placeholder="Today"/>

                                <label for="invoice_date" string="Bill Date" style="font-weight:bold;"
                                       invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')"/>
                                <field name="invoice_date" nolabel="1" options="{'warn_future': true}"
                                       invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')"
                                       readonly="state != 'draft'"/>

                                <field name="date" string="Accounting Date"
                                       invisible="move_type in ('out_invoice', 'out_refund', 'out_receipt') and not quick_edit_mode and not (state == 'posted' and date != invoice_date)"
                                       readonly="state != 'draft'"/>
                                <field name="payment_reference"
                                       invisible="move_type not in ('in_invoice', 'in_refund', 'out_receipt', 'in_receipt')"
                                       readonly="inalterable_hash != False"
                                       placeholder="Use Bill Reference"/>
                                <field name="partner_bank_id"
                                       context="{'default_partner_id': bank_partner_id, 'display_account_trust': True}"
                                       domain="[('partner_id', '=', bank_partner_id)]"
                                       invisible="move_type not in ('in_invoice', 'in_refund', 'in_receipt')"
                                       readonly="is_move_sent and state != 'draft'"/>

                                <!-- Invoice payment terms (only invoices) + due date (only invoices / receipts) -->
                                <div class="o_td_label" invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')">
                                    <label for="invoice_date_due" string="Due Date"
                                           invisible="invoice_payment_term_id"/>
                                    <label for="invoice_payment_term_id" string="Payment terms"
                                           invisible="not invoice_payment_term_id"/>
                                </div>
                                <div class="d-flex" invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt')" name="due_date">
                                    <field name="invoice_date_due" force_save="1"
                                           placeholder="Date"
                                           invisible="invoice_payment_term_id"/>
                                    <span class="o_form_label mx-3 oe_edit_only text-center" style="width: 6ch;"
                                          invisible="state != 'draft' or invoice_payment_term_id">or</span>
                                    <field name="invoice_payment_term_id"
                                           context="{'example_date': invoice_date, 'example_amount': tax_totals['total_amount_currency']}"
                                           placeholder="Payment Terms"
                                           options="{'no_quick_create':True}" readonly="state in ['cancel', 'posted']"/>
                                </div>
                                <field name="delivery_date" invisible="not show_delivery_date" readonly="state != 'draft'"/>
                                <label for="journal_id" groups="account.group_account_readonly"/>
                                <label for="currency_id" groups="!account.group_account_readonly,base.group_multi_currency"/>
                                <div name="journal_div"
                                     class="d-flex"
                                     groups="account.group_account_readonly,base.group_multi_currency">
                                    <field name="journal_id"
                                           groups="account.group_account_readonly"
                                           options="{'no_create': True, 'no_open': True}"
                                           readonly="posted_before and name not in (False, '', '/')"/>
                                    <div name="in_and_refresh_button_div"
                                         groups="base.group_multi_currency"
                                         class="d-flex flex-column mx-3 text-center"
                                         style="width: 6ch;"
                                         invisible="move_type == 'entry'">
                                        <div>in</div>
                                        <div class="d-flex flex-column justify-content-center flex-grow-1"
                                             invisible="state != 'draft' or invoice_currency_rate == expected_currency_rate">
                                            <button type="object"
                                                    name="refresh_invoice_currency_rate"
                                                    icon="fa-refresh"
                                                    title="Refresh currency rate to the invoice date"
                                                    class="btn btn-link p-0"/>
                                        </div>
                                    </div>
                                    <div name="currency_div"
                                         groups="base.group_multi_currency"
                                         class="w-100"
                                         invisible="move_type == 'entry'">
                                        <div class="d-flex gap-1">
                                            <field name="currency_id"
                                                   readonly="state != 'draft'"
                                                   class="oe_inline"
                                                   options="{'no_create': True}"
                                                   context="{'search_default_active': 1, 'search_default_inactive': 1}"/>
                                            <widget name="account_pick_currency_date" invisible="state != 'draft' or currency_id == company_currency_id"/>
                                        </div>
                                        <div name="currency_conversion_div"
                                             class="d-flex gap-1 text-muted"
                                             invisible="currency_id == company_currency_id">
                                            <span>1</span>
                                            <field name="company_currency_id" readonly="True" options="{'no_open': True}" class="w-auto"/>
                                            <span>=</span>
                                            <field name="invoice_currency_rate" digits="[12,6]" readonly="state != 'draft'"/>
                                            <field name="currency_id" readonly="True" options="{'no_open': True}" class="w-auto"/>
                                        </div>
                                    </div>
                                </div>
                            </group>
                        </group>
                        <notebook>
                            <page id="invoice_tab"
                                  name="invoice_tab"
                                  string="Invoice Lines"
                                  invisible="move_type == 'entry'">
                                <field name="invoice_line_ids"
                                       widget="product_label_section_and_note_field_o2m"
                                       mode="list,kanban"
                                       context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'default_display_type': 'product',
                                           'quick_encoding_vals': quick_encoding_vals,
                                       }" readonly="state != 'draft'">
                                    <list editable="bottom" string="Journal Items" default_order="sequence, id">
                                        <control>
                                            <create name="add_line_control" string="Add a line"/>
                                            <create name="add_section_control" string="Add a section" context="{'default_display_type': 'line_section'}"/>
                                            <create name="add_note_control" string="Add a note" context="{'default_display_type': 'line_note'}"/>
                                            <button name="action_add_from_catalog" string="Catalog" type="object" class="btn-link" context="{'order_id': parent.id}"/>
                                        </control>

                                        <!-- Displayed fields -->
                                        <field name="sequence" widget="handle"/>
                                        <field name="product_id"
                                               optional="show"
                                               widget="product_label_section_and_note_field"
                                               domain="
                                                    context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')
                                                    and [('sale_ok', '=', True)]
                                                    or [('purchase_ok', '=', True)]
                                               "/>
                                        <field name="name" widget="section_and_note_text" optional="show"/>
                                        <field name="account_id"
                                               context="{'partner_id': partner_id, 'move_type': parent.move_type}"
                                               groups="account.group_account_readonly"
                                               options="{'no_quick_create': True}"
                                               domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id), ('account_type', 'not in', ('asset_receivable', 'liability_payable', 'off_balance'))]"
                                               required="display_type not in ('line_note', 'line_section')"/>
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                               string="Analytic"
                                               groups="analytic.group_analytic_accounting"
                                               optional="show"
                                               options="{'product_field': 'product_id', 'account_field': 'account_id', 'amount_field': 'price_subtotal'}"
                                               business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'"/>
                                        <field name="quantity" optional="show"/>
                                        <field name="product_uom_category_id" column_invisible="True"/>
                                        <field name="product_uom_id" string="UoM" groups="uom.group_uom" optional="show" width="92px"/>
                                        <!-- /l10n_in_edi.test_edi_json -->
                                        <!-- required for @api.onchange('product_id') -->
                                        <field name="product_uom_id" column_invisible="True"/>
                                        <field name="price_unit" string="Price"/>
                                        <field name="discount" width="50px" string="Disc.%" optional="hide"/>
                                        <field name="tax_ids" widget="many2many_tax_tags"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain), ('company_id', 'parent_of', parent.company_id), ('country_id', '=', parent.tax_country_id)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain, 'active_test': True}"
                                               options="{'no_create': True}"
                                               optional="show"/>
                                        <field name="price_subtotal"
                                               column_invisible="parent.move_type not in ['in_invoice', 'in_refund', 'in_receipt'] and parent.company_price_include == 'tax_included'"
                                               string="Amount"/>
                                        <field name="price_total"
                                               column_invisible="parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] or parent.company_price_include == 'tax_excluded'"
                                               string="Amount"/>
                                        <!-- Others fields -->
                                        <field name="partner_id" column_invisible="True"/>
                                        <field name="currency_id" column_invisible="True"/>
                                        <field name="company_id" column_invisible="True"/>
                                        <field name="company_currency_id" column_invisible="True"/>
                                        <field name="display_type" force_save="1" column_invisible="True"/>
                                    </list>
                                    <kanban class="o_kanban_mobile">
                                        <!-- Displayed fields -->
                                        <templates>
                                            <t t-name="card">
                                                <div t-attf-class="ps-0 pe-0 {{ record.display_type.raw_value ? 'o_is_' + record.display_type.raw_value : '' }}">
                                                    <t t-if="!['line_note', 'line_section'].includes(record.display_type.raw_value)">
                                                        <div class="row g-0">
                                                            <div class="col-2 pe-3">
                                                                <field name="product_id" widget="image" options="{'preview_image': 'image_128'}" class="w-100"/>
                                                            </div>
                                                            <div class="col-10">
                                                                <div class="row">
                                                                    <div class="col">
                                                                        <field class="fw-bold" name="product_id"/>
                                                                    </div>
                                                                    <div class="col-auto fw-bold float-end text-end">
                                                                        <field name="price_subtotal"
                                                                               t-if="['in_invoice', 'in_refund', 'in_receipt'].includes(record.move_type.raw_value)"
                                                                               string="Amount"/>
                                                                        <field name="price_total"
                                                                               t-if="!['in_invoice', 'in_refund', 'in_receipt'].includes(record.move_type.raw_value)"
                                                                               string="Amount"/>
                                                                    </div>
                                                                </div>
                                                                <div class="text-muted">
                                                                    Quantity:
                                                                    <field name="quantity"/>
                                                                    <field name="product_uom_id" groups="uom.group_uom"/>
                                                                </div>
                                                                <div class="text-muted">
                                                                    Unit Price:
                                                                    <field name="price_unit"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </t>
                                                    <t t-if="record.display_type.raw_value === 'line_section' || record.display_type.raw_value === 'line_note'">
                                                        <field name="name"/>
                                                    </t>
                                                </div>
                                            </t>
                                        </templates>

                                        <!-- Others fields -->
                                        <field name="tax_calculation_rounding_method"/>
                                        <field name="currency_id"/>
                                        <field name="company_currency_id"/>
                                        <field name="display_type" force_save="1"/>
                                        <field name="move_type"/>
                                    </kanban>

                                    <!-- Form view to cover mobile use -->
                                    <form>
                                        <sheet>
                                            <field name="tax_calculation_rounding_method" invisible="1"/>
                                            <field name="display_type" invisible="1"/>
                                            <field name="company_id" invisible="1"/>
                                            <field name="partner_id" invisible="1"/>
                                            <group>
                                                <field name="product_id" widget="many2one_barcode"/>
                                                <label for="name" string="Description" invisible="display_type in ('line_note', 'line_section')"/>
                                                <label for="name" string="Section" invisible="display_type != 'line_section'"/>
                                                <label for="name" string="Note" invisible="display_type != 'line_note'"/>
                                                <field name="name" widget="text" nolabel="1"/>
                                                <field name="quantity"/>
                                                <field name="product_uom_category_id" invisible="1"/>
                                                <field name="product_uom_id" groups="uom.group_uom"/>
                                                <field name="price_unit"/>
                                                <field name="discount" string="Disc.%"/>
                                            </group>
                                            <group>
                                                <field name="account_id" domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id)]" options="{'no_create': True}" context="{'partner_id': partner_id, 'move_type': parent.move_type}"/>
                                                <field name="tax_ids" widget="many2many_tags"/>
                                                <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"/>
                                            </group>
                                            <group>
                                                <field name="price_subtotal"
                                                       invisible="parent.move_type not in ['in_invoice', 'in_refund', 'in_receipt'] and parent.company_price_include == 'tax_included'"
                                                       string="Amount"/>
                                                <field name="price_total"
                                                       invisible="parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] or parent.company_price_include == 'tax_excluded'"
                                                       string="Amount"/>
                                            </group>
                                        </sheet>
                                    </form>
                                </field>
                                <group col="12" class="oe_invoice_lines_tab overflow-hidden">
                                    <group colspan="8">
                                        <field name="narration" placeholder="Terms and Conditions" nolabel="1"/>
                                    </group>
                                    <!-- Totals (only invoices / receipts) -->
                                    <group colspan="4">
                                        <group class="oe_subtotal_footer"
                                            invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') or payment_state == 'invoicing_legacy'">

                                            <field name="tax_totals" widget="account-tax-totals-field" nolabel="1" colspan="2"
                                                   readonly="state != 'draft' or (move_type not in ('in_invoice', 'in_refund', 'in_receipt') and not quick_edit_mode)"/>

                                            <field name="invoice_payments_widget" colspan="2" nolabel="1" widget="payment"/>
                                            <field name="amount_residual" class="oe_subtotal_footer_separator" invisible="state == 'draft'"/>
                                        </group>
                                        <group
                                            class="oe_subtotal_footer px-4"
                                            invisible="state != 'posted' or not invoice_has_outstanding"
                                            groups="account.group_account_invoice,account.group_account_readonly">
                                            <field name="invoice_outstanding_credits_debits_widget"
                                                class="oe_invoice_outstanding_credits_debits py-3"
                                                colspan="2" nolabel="1" widget="payment"/>
                                        </group>
                                    </group>
                                </group>
                            </page>
                            <page id="aml_tab" string="Journal Items" groups="account.group_account_readonly" name="aml_tab">
                                <field name="line_ids"
                                       mode="list,kanban"
                                       context="{
                                           'default_move_type': context.get('default_move_type'),
                                           'line_ids': line_ids,
                                           'journal_id': journal_id,
                                           'default_partner_id': commercial_partner_id,
                                           'default_currency_id': currency_id or company_currency_id,
                                           'kanban_view_ref': 'account.account_move_line_view_kanban_mobile',
                                       }"
                                       invisible="payment_state == 'invoicing_legacy' and move_type != 'entry'"
                                       readonly="state != 'draft'">
                                    <list editable="bottom" string="Journal Items" decoration-muted="display_type in ('line_section', 'line_note')" default_order="sequence, id">
                                        <!-- Displayed fields -->
                                        <field name="account_id"
                                               invisible="display_type in ('line_section', 'line_note')"
                                               required="display_type not in ('line_section', 'line_note')"
                                               domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id)]" />
                                        <field name="partner_id"
                                               optional="show"
                                               domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"
                                               column_invisible="parent.move_type != 'entry'"/>
                                        <field name="name" widget="section_and_note_text" optional="show"/>
                                        <field name="analytic_distribution" widget="analytic_distribution"
                                               groups="analytic.group_analytic_accounting"
                                               optional="show"
                                               options="{'account_field': 'account_id'}"
                                               business_domain_compute="parent.move_type in ['out_invoice', 'out_refund', 'out_receipt'] and 'invoice' or parent.move_type in ['in_invoice', 'in_refund', 'in_receipt'] and 'bill' or 'general'"/>
                                        <field name="date_maturity"
                                               optional="hide"
                                               invisible="display_type in ('line_section', 'line_note')"
                                               column_invisible="context.get('view_no_maturity')"/>
                                        <field name="amount_currency"
                                               groups="base.group_multi_currency"
                                               optional="hide"/>
                                        <field name="currency_id" options="{'no_create': True}"
                                               optional="hide" groups="base.group_multi_currency"
                                               column_invisible="parent.move_type != 'entry'"/>
                                        <field name="tax_ids" widget="autosave_many2many_tags"
                                               optional="hide"
                                               domain="[('type_tax_use', '=?', parent.invoice_filter_type_domain)]"
                                               context="{'append_type_to_tax_name': not parent.invoice_filter_type_domain, 'active_test': True}"
                                               options="{'no_create': True}"
                                               force_save="1"
                                               readonly="display_type in ('line_section', 'line_note') or tax_line_id or (parent.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') and account_type in ('asset_receivable', 'liability_payable'))"/>
                                        <field name="debit"
                                               sum="Total Debit"
                                               invisible="display_type in ('line_section', 'line_note')"
                                               readonly="parent.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') and display_type in ('line_section', 'line_note', 'product')"/>
                                        <field name="credit"
                                               sum="Total Credit"
                                               invisible="display_type in ('line_section', 'line_note')"
                                               readonly="parent.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund', 'out_receipt', 'in_receipt') and display_type in ('line_section', 'line_note', 'product')"/>
                                        <field name="balance" column_invisible="True"/>
                                        <field name="discount_date"
                                               string="Discount Date"
                                               optional="hide"
                                        />
                                        <field name="discount_amount_currency"
                                               string="Discount Amount"
                                               optional="hide"
                                        />

                                        <field name="tax_tag_ids"
                                               widget="many2many_tags"
                                               string="Tax Grids"
                                               optional="show"
                                               options="{'no_create': True}"
                                               domain="[
                                                    ('applicability', '=', 'taxes'),
                                                    '|', ('country_id', '=', parent.tax_country_id),
                                                    ('country_id', '=', False),
                                                ]"/>

                                        <field name="tax_tag_invert" readonly="1" optional="hide" groups="base.group_no_one"/>

                                        <!-- Buttons -->
                                        <button name="action_automatic_entry"
                                                type="object"
                                                icon="fa-calendar"
                                                string="Cut-Off"
                                                aria-label="Change Period"
                                                class="float-end"
                                                column_invisible="parent.move_type == 'entry' or parent.state != 'posted'"
                                                invisible="account_internal_group not in ('income', 'expense')"
                                                context="{'default_action': 'change_period'}"/>

                                        <!-- Others fields -->
                                        <field name="tax_line_id" column_invisible="True"/>
                                        <field name="company_currency_id" column_invisible="True"/>
                                        <field name="display_type" force_save="1" column_invisible="True"/>
                                        <field name="company_id" column_invisible="True"/>
                                        <field name="sequence" column_invisible="True"/>
                                        <field name="id" column_invisible="True"/>
                                        <field name="account_internal_group" column_invisible="True"/>
                                        <field name="account_type" column_invisible="True"/>
                                    </list>
                                    <!-- Form view to cover mobile use -->
                                    <form>
                                      <group>
                                        <field name="account_id" domain="[('deprecated', '=', False), ('company_ids', 'parent_of', company_id)]"/>
                                        <field name="partner_id" domain="['|', ('parent_id', '=', False), ('is_company', '=', True)]"/>
                                        <field name="name"/>
                                        <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting"/>
                                        <field name="amount_currency" groups="base.group_multi_currency"/>
                                        <field name="company_currency_id" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="currency_id" options="{'no_create': True}" groups="base.group_multi_currency"/>
                                        <field name="debit" sum="Total Debit"/>
                                        <field name="credit" sum="Total Credit"/>
                                        <field name="balance" invisible="1"/>
                                        <field name="tax_ids" string="Taxes Applied" widget="autosave_many2many_tags" options="{'no_create': True}"/>
                                        <field name="date_maturity" required="0" invisible="context.get('view_no_maturity', False)"/>
                                      </group>
                                    </form>
                                </field>
                                <div class="alert alert-info text-center mb-0" role="alert" invisible="payment_state != 'invoicing_legacy' or move_type == 'entry'">
                                    <span>This entry has been generated through the Invoicing app, before installing Accounting. Its balance has been imported separately.</span>
                                </div>
                            </page>
                            <page id="other_tab" string="Other Info" name="other_info"
                                  invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')">
                                <group id="other_tab_group">
                                    <group string="Invoice"
                                           name="sale_info_group"
                                           invisible="move_type not in ('out_invoice', 'out_refund')">
                                        <label for="ref" string="Customer Reference" />
                                        <field name="ref" nolabel="1"/>
                                        <field name="user_id" invisible="1" force_save="1"/>
                                        <field name="invoice_user_id" domain="[('share', '=', False)]" widget="many2one_avatar_user"/>
                                        <field name="invoice_origin" string="Source Document" force_save="1" invisible="1"/>
                                        <field name="partner_bank_id"
                                               context="{'default_partner_id': bank_partner_id, 'display_account_trust': True}"
                                               domain="[('partner_id.ref_company_ids', 'parent_of', company_id)]"
                                               readonly="is_move_sent and state != 'draft'"/>
                                        <field name="payment_reference"
                                            invisible="move_type not in ('out_invoice', 'out_refund')"
                                            readonly="inalterable_hash"
                                            placeholder="Standard communication"/>
                                        <field name="qr_code_method"
                                               invisible="not display_qr_code"/>
                                        <field name="delivery_date" readonly="state != 'draft'"/>
                                    </group>
                                    <group string="Accounting"
                                           name="accounting_info_group"
                                           invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')">
                                        <field name="company_id" groups="base.group_multi_company"/>
                                        <field name="invoice_incoterm_id"/>
                                        <field name="incoterm_location"/>
                                        <field name="fiscal_position_id" readonly="state in ['cancel', 'posted']"/>
                                        <field name="secured" groups="account.group_account_secured,base.group_no_one"/>
                                        <field name="preferred_payment_method_line_id"
                                               string="Payment Method"
                                               domain="[('payment_type', '=', 'inbound'), ('company_id', '=', company_id)]"
                                               invisible="move_type in ('in_invoice', 'in_refund')"
                                        />
                                        <field name="preferred_payment_method_line_id"
                                               string="Payment Method"
                                               domain="[('payment_type', '=', 'outbound'), ('company_id', '=', company_id)]"
                                               invisible="move_type in ('out_invoice', 'out_refund')"
                                        />
                                        <field name="invoice_cash_rounding_id" groups="account.group_cash_rounding" readonly="state != 'draft'"/>
                                        <field name="invoice_source_email"
                                               widget="email"
                                               invisible="move_type not in ('in_invoice', 'in_refund') or not invoice_source_email"/>
                                        <field name="auto_post"
                                               readonly="state != 'draft'"/>
                                        <field name="auto_post_until"
                                               invisible="auto_post in ('no', 'at_date')"
                                               readonly="state != 'draft'"/>
                                        <field name="checked"/>
                                    </group>
                                </group>
                            </page>
                            <page id="other_tab_entry" string="Other Info" name="other_info"
                                  invisible="move_type != 'entry'">
                                <group id="other_tab_entry_group">
                                    <group name="misc_group">
                                        <field name="auto_post"
                                               invisible="move_type != 'entry'"
                                               readonly="state != 'draft'"/>
                                        <field name="reversed_entry_id"
                                               invisible="move_type != 'entry' or not reversed_entry_id"/>
                                        <field name="auto_post_until"
                                               invisible="auto_post in ('no', 'at_date')"
                                               readonly="state != 'draft'"/>
                                        <field name="checked"
                                               invisible="move_type != 'entry'" />
                                    </group>
                                    <group>
                                        <field name="fiscal_position_id" readonly="state in ['cancel', 'posted']"/>
                                        <field name="company_id" groups="base.group_multi_company" required="1"/>
                                    </group>
                                </group>
                                <!-- Internal note -->
                                <field name="narration" placeholder="Add an internal note..." nolabel="1" height="50"/>
                            </page>
                        </notebook>
                    </sheet>
                    <!-- Attachment preview -->
                    <div class="o_attachment_preview"
                         invisible="move_type not in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund') or state != 'draft'" />
                    <!-- Chatter -->
                    <chatter reload_on_attachment="True"/>
                </form>
            </field>
        </record>

        <record id="account_move_view_activity" model="ir.ui.view">
            <field name="name">account.move.view.activity</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <activity string="Account Entry">
                    <field name="currency_id"/>
                    <templates>
                        <div t-name="activity-box" class="d-block">
                            <div class="d-flex justify-content-between">
                                <field name="name" string="Entry Name" class="o_text_block o_text_bold"/>
                                <div class="m-1"/>
                                <field name="amount_total" widget="monetary"/>
                            </div>
                            <div class="d-flex justify-content-between">
                                <field name="commercial_partner_id" string="Commercial Entity" class="o_text_block"/>
                                <div class="m-1"/>
                                <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-success="state == 'posted'"/>
                            </div>
                        </div>
                    </templates>
                </activity>
            </field>
        </record>

        <record id="view_account_move_filter" model="ir.ui.view">
            <field name="name">account.move.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Move">
                    <field name="name" string="Journal Entry" filter_domain="['|', '|', ('name', 'ilike', self), ('ref', 'ilike', self), ('partner_id', 'ilike', self)]"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="invoice_date"/>
                    <field name="date"/>
                    <field name="amount_total"/>
                    <field name="partner_id"/>
                    <field name="journal_id"/>
                    <filter string="Unposted" name="unposted" domain="[('state', '=', 'draft')]" help="Unposted Journal Entries"/>
                    <filter string="Posted" name="posted" domain="[('state', '=', 'posted')]" help="Posted Journal Entries"/>
                    <separator groups="account.group_account_secured,base.group_no_one"/>
                    <filter name="not_secured" string="Not Secured" domain="[('secured', '=', False), ('state', '=', 'posted')]"
                            groups="account.group_account_secured,base.group_no_one"/>
                    <separator/>
                    <filter string="Reversed" name="reversed" domain="[('payment_state', '=', 'reversed')]"/>
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('checked', '=', False), ('state', '!=', 'draft')]"/>
                    <separator/>
                    <filter string="Sales" name="sales" domain="[('journal_id.type', '=', 'sale')]" context="{'default_journal_type': 'sale'}"/>
                    <filter string="Purchases" name="purchases" domain="[('journal_id.type', '=', 'purchase')]" context="{'default_journal_type': 'purchase'}"/>
                    <filter string="Bank" name="bankoperations" domain="[('journal_id.type', '=', 'bank')]" context="{'default_journal_type': 'bank'}"/>
                    <filter string="Cash" name="cashoperations" domain="[('journal_id.type', '=', 'cash')]" context="{'default_journal_type': 'cash'}"/>
                    <filter string="Credit" name="credit" domain="[('journal_id.type', '=', 'credit')]" context="{'default_journal_type': 'credit'}"/>
                    <filter string="Miscellaneous" name="misc_filter" domain="[('journal_id.type', '=', 'general')]" context="{'default_journal_type': 'general'}"/>
                    <separator/>
                    <filter string="Date" name="date" date="date"/>
                    <filter string="Invoice Date" name="invoice_date" date="invoice_date"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Partner" name="partner" domain="[]" context="{'group_by': 'partner_id'}"/>
                        <filter string="Journal" name="journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                        <filter string="Payment Method"
                                name="preferred_payment_method"
                                context="{'group_by': 'preferred_payment_method_line_id'}"
                                groups="account.group_account_invoice,account.group_account_readonly"
                        />
                        <filter string="Date" name="by_date" domain="[]" context="{'group_by': 'date'}" help="Journal Entries by Date"/>
                        <filter string="Invoice Date" name="by_invoice_date" domain="[]" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Company" name="by_company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="view_account_invoice_filter" model="ir.ui.view">
            <field name="name">account.invoice.select</field>
            <field name="model">account.move</field>
            <field name="arch" type="xml">
                <search string="Search Invoice">
                    <field name="name" string="Invoice"
                           filter_domain="[
                                '|', '|' , '|', '|',
                                ('name', 'ilike', self), ('invoice_origin', 'ilike', self),
                                ('ref', 'ilike', self), ('payment_reference', 'ilike', self),
                                ('partner_id', 'child_of', self)]"/>
                    <field name="name"/>
                    <field name="ref"/>
                    <field name="payment_reference"/>
                    <field name="amount_total"/>
                    <field name="journal_id"/>
                    <field name="journal_group_id"/>
                    <field name="partner_id" operator="child_of"/>
                    <field name="invoice_user_id" string="Salesperson" domain="[('share', '=', False)]"/>
                    <field name="date" string="Period"/>
                    <field name="next_payment_date"/>
                    <field name="line_ids" string="Invoice Line"/>
                    <filter domain="[('invoice_user_id', '=', uid)]" name="myinvoices" help="My Invoices"/>
                    <separator/>
                    <filter name="draft" string="Draft" domain="[('state','=','draft')]"/>
                    <filter name="posted" string="Posted" domain="[('state', '=', 'posted')]"/>
                    <filter name="cancel" string="Cancelled" domain="[('state', '=', 'cancel')]"/>
                    <separator groups="account.group_account_secured,base.group_no_one"/>
                    <filter name="not_secured" string="Not Secured" domain="[('secured', '=', False), ('state', '=', 'posted')]"
                            groups="account.group_account_secured,base.group_no_one"/>
                    <separator/>
                    <filter name="not_sent"
                            string="Not Sent"
                            domain="[('is_move_sent', '=', False)]"
                            invisible="context.get('default_move_type') in ('in_invoice', 'in_refund', 'in_receipt')"
                    />
                    <separator/>
                    <filter name="out_invoice"
                            string="Invoices"
                            domain="[('move_type', '=', 'out_invoice')]"
                    />
                    <filter name="out_refund"
                            string="Credit Notes"
                            domain="[('move_type', '=', 'out_refund')]"
                    />
                    <separator/>
                    <filter string="To Check" name="to_check" domain="[('checked', '=', False), ('state', '!=', 'draft')]"/>
                    <separator/>
                    <!-- not_paid & partial -->
                    <filter name="open" string="To pay" domain="[('state', '=', 'posted'), ('payment_state', 'in', ('not_paid', 'partial'))]"/>
                    <!-- in_payment & paid -->
                    <filter name="in_payment" string="In payment" domain="[('state', '=', 'posted'), ('payment_state', '=', 'in_payment')]"/>
                    <!-- overdue -->
                    <filter name="late" string="Overdue" domain="[
                        ('invoice_date_due', '&lt;', time.strftime('%Y-%m-%d')),
                        ('state', '=', 'posted'),
                        ('payment_state', 'in', ('not_paid', 'partial'))
                    ]" help="Overdue invoices, maturity date passed"/>
                    <separator/>
                    <filter name="invoice_date" string="Invoice Date" date="invoice_date"/>
                    <filter name="date" invisible="context.get('default_move_type') in ('out_invoice', 'out_refund', 'out_receipt')" string="Accounting Date" date="date"/>
                    <filter name="due_date" string="Due Date" date="invoice_date_due"/>
                    <separator/>
                    <filter invisible="1" string="Late Activities" name="activities_overdue"
                        domain="[('my_activity_date_deadline', '&lt;', context_today().strftime('%Y-%m-%d'))]"
                        help="Show all records which has next action date is before today"/>
                    <filter invisible="1" string="Today Activities" name="activities_today"
                        domain="[('my_activity_date_deadline', '=', context_today().strftime('%Y-%m-%d'))]"/>
                    <filter invisible="1" string="Future Activities" name="activities_upcoming_all"
                        domain="[('my_activity_date_deadline', '&gt;', context_today().strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter string="Salesperson" name="salesperson" context="{'group_by':'invoice_user_id'}"/>
                        <filter string="Partner" name="partner" context="{'group_by':'partner_id'}"/>
                        <filter name="status" string="Status" context="{'group_by':'state'}"/>
                        <filter string="Payment Method"
                                name="preferred_payment_method_line"
                                context="{'group_by': 'preferred_payment_method_line_id'}"
                                groups="account.group_account_invoice,account.group_account_readonly"
                        />
                        <filter string="Journal" name="groupy_by_journal" domain="[]" context="{'group_by': 'journal_id'}"/>
                        <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <separator/>
                        <filter string="Invoice Date" name="invoicedate" context="{'group_by': 'invoice_date'}"/>
                        <filter string="Due Date" name="duedate" context="{'group_by': 'invoice_date_due'}"/>
                        <filter string="Accounting Date" name="accounting_date" context="{'group_by': 'date'}"/>
                        <filter string="Sequence Prefix" name="group_by_sequence_prefix" context="{'group_by': 'sequence_prefix'}" invisible="1"/>
                    </group>
               </search>
            </field>
        </record>

        <record id="view_account_bill_filter" model="ir.ui.view">
            <field name="name">account.invoice.select</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <field name="name" position="attributes">
                    <attribute name="string">Bill</attribute>
                </field>
                <filter name="myinvoices" position="replace"/>
                <filter name="invoice_date" position="attributes">
                    <attribute name="string">Bill Date</attribute>
                </filter>
                <filter name="salesperson" position="replace"/>
                <filter name="invoicedate" position="attributes">
                    <attribute name="string">Bill Date</attribute>
                </filter>
                <filter name="out_invoice" position="replace">
                    <filter name="in_invoice"
                            string="Bills"
                            domain="[('move_type', '=', 'in_invoice')]"
                    />
                </filter>
                <filter name="out_refund" position="replace">
                    <filter name="in_refund"
                            string="Refunds"
                            domain="[('move_type', '=', 'in_refund')]"
                    />
                </filter>
            </field>
        </record>

        <record id="view_account_move_with_gaps_in_sequence_filter" model="ir.ui.view">
            <field name="name">account.move.with.gaps.in.sequence.filter</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_invoice_filter"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">
                <filter name="due_date" position="after">
                    <separator/>
                    <filter name="irregular_sequences" string="Irregular Sequences" domain="['|', ('made_sequence_gap', '=', True), '&amp;', '&amp;', ('state', 'in', ('draft', 'cancel')), ('sequence_number', '!=', 0), ('name', '!=', '/')]"/>
                </filter>
            </field>
        </record>

        <!-- ACTIONS -->

        <!-- account.move.line (Journal Items) -->

        <record id="action_move_line_select" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="context">{'search_default_account_id': [active_id], 'search_default_posted': 1}</field>
        </record>

        <record id="action_automatic_entry_change_account" model="ir.actions.server">
            <field name="name">Move to Account</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="binding_model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">action = records.action_automatic_entry('change_account')</field>
        </record>

        <record id="action_automatic_entry_change_period" model="ir.actions.server">
            <field name="name">Change Period</field>
            <field name="model_id" ref="account.model_account_move_line"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_user'))]"/>
            <field name="binding_model_id" ref="account.model_account_move_line"/>
            <field name="state">code</field>
            <field name="code">action = records.action_automatic_entry('change_period')</field>
        </record>

        <record id="action_account_moves_all_a" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'create':0}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_all_grouped_matching" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_posted':1, 'expand':'1'}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_sales" model="ir.actions.act_window">
            <field name="context">{'journal_type':'sales', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_sales':1, 'expand': 1}</field>
            <field name="name">Sales</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_purchase" model="ir.actions.act_window">
            <field name="context">{'journal_type':'purchase', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_purchases':1, 'expand': 1}</field>
            <field name="name">Purchases</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_sales_purchases"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_bank_cash" model="ir.actions.act_window">
            <field name="context">{'journal_type':'bank', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_bank':1, 'search_default_cash':1, 'expand': 1}</field>
            <field name="name">Bank and Cash</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_bank_cash"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_journal_misc" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_move': 1, 'search_default_posted':1, 'search_default_misc_filter':1, 'expand': 1}</field>
            <field name="name">Miscellaneous</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_misc"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <record id="action_account_moves_ledger_partner" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_group_by_partner': 1, 'search_default_posted':1, 'search_default_trade_payable':1, 'search_default_trade_receivable':1, 'search_default_unreconciled':1}</field>
            <field name="name">Partner Ledger</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="view_id" ref="view_move_line_tree_grouped_partner"/>
            <field name="search_view_id" ref="view_account_move_line_filter"/>
            <field name="view_mode">list,pivot,graph</field>
        </record>

        <record id="action_account_moves_all_tree" model="ir.actions.act_window">
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note'))]</field>
            <field name="context">{'search_default_partner_id': [active_id], 'default_partner_id': active_id, 'search_default_posted':1}</field>
            <field name="view_id" ref="view_move_line_tree"/>
        </record>

        <record id="action_account_moves_all" model="ir.actions.act_window">
            <field name="context">{'journal_type':'general', 'search_default_posted':1}</field>
            <field name="name">Journal Items</field>
            <field name="res_model">account.move.line</field>
            <field name="domain">[('display_type', 'not in', ('line_section', 'line_note')), ('parent_state', '!=', 'cancel')]</field>
            <field name="view_id" ref="view_move_line_tree"/>
            <field name="view_mode">list,pivot,graph,kanban</field>
        </record>

        <!-- account.move (Journal Entry) -->

        <record id="action_move_journal_line" model="ir.actions.act_window">
            <field name="name">Journal Entries</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
            <field name="context">{'default_move_type': 'entry', 'search_default_posted':1, 'view_no_maturity': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a journal entry
              </p><p>
                A journal entry consists of several journal items, each of
                which is either a debit or a credit transaction.
              </p>
            </field>
        </record>

        <record id="action_move_out_invoice_type" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="path">invoicing</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_out_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', 'in', ['out_invoice', 'out_refund'])]</field>
            <field name="context">{'default_move_type': 'out_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a customer invoice
              </p><p>
                Create invoices, register payments and keep track of the discussions with your customers.
              </p>
            </field>
        </record>

        <!-- ADDED IN STABLE - DO NOT USE IN 18.0 -->
        <record id="action_move_out_invoice" model="ir.actions.act_window">
            <field name="name">Invoices</field>
            <field name="path">customer-invoices</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_out_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', 'in', ['out_invoice', 'out_refund'])]</field>
            <field name="context">{'search_default_out_invoice': 1, 'default_move_type': 'out_invoice'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a customer invoice
              </p><p>
                Create invoices, register payments and keep track of the discussions with your customers.
              </p>
            </field>
        </record>

        <record id="action_move_out_refund_type" model="ir.actions.act_window">
            <field name="name">Credit Notes</field>
            <field name="res_model">account.move</field>
            <field name="path">credit-notes</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_out_credit_note_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', 'in', ['out_invoice', 'out_refund'])]</field>
            <field name="context">{'search_default_out_refund': 1, 'default_move_type': 'out_refund', 'display_account_trust': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a credit note
              </p><p>
                Note that the easiest way to create a credit note is to do it directly
                from the customer invoice.
              </p>
            </field>
        </record>

        <record id="action_move_in_invoice_type" model="ir.actions.act_window">
            <field name="name">Bills</field>
            <field name="res_model">account.move</field>
            <field name="path">bills</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_in_invoice_bill_tree"/>
            <field name="search_view_id" ref="view_account_bill_filter"/>
            <field name="domain">[('move_type', 'in', ['in_invoice', 'in_refund'])]</field>
            <field name="context">{'default_move_type': 'in_invoice', 'display_account_trust': True}</field>
            <field name="help" type="html">
                <!-- An owl component should be displayed instead -->
                <p class="o_view_nocontent_smiling_face">
                    Create a vendor bill
                </p><p>
                    Capture invoices, register payments and keep track of the discussions with your vendors.
                </p>
            </field>
        </record>

        <!-- ADDED IN STABLE - DO NOT USE IN 18.0 -->
        <record id="action_move_in_invoice" model="ir.actions.act_window">
            <field name="name">Bills</field>
            <field name="res_model">account.move</field>
            <field name="path">vendor-bills</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_in_invoice_bill_tree"/>
            <field name="search_view_id" ref="view_account_bill_filter"/>
            <field name="domain">[('move_type', 'in', ['in_invoice', 'in_refund'])]</field>
            <field name="context">{'search_default_in_invoice': 1, 'default_move_type': 'in_invoice', 'display_account_trust': True}</field>
            <field name="help" type="html">
                <!-- An owl component should be displayed instead -->
                <p class="o_view_nocontent_smiling_face">
                    Create a vendor bill
                </p><p>
                    Capture invoices, register payments and keep track of the discussions with your vendors.
                </p>
            </field>
        </record>

        <record id="action_move_in_refund_type" model="ir.actions.act_window">
            <field name="name">Refunds</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_in_invoice_refund_tree"/>
            <field name="search_view_id" ref="view_account_bill_filter"/>
            <field name="domain">[('move_type', 'in', ['in_invoice', 'in_refund'])]</field>
            <field name="context">{'search_default_in_refund': 1, 'default_move_type': 'in_refund'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a vendor credit note
              </p><p>
                Note that the easiest way to create a vendor credit note is to do it directly from the vendor bill.
              </p>
            </field>
        </record>

        <record id="action_amounts_to_settle" model="ir.actions.act_window">
            <field name="name">Amounts to Settle</field>
            <field name="res_model">account.move.line</field>
            <field name="view_mode">list</field>
            <field name="view_id" ref="view_move_line_payment_tree"/>
            <field name="search_view_id" ref="view_account_move_line_payment_filter"/>
            <field name="domain">[('parent_state', '=', 'posted'), ('date_maturity', '!=', False), ('amount_residual', '!=', 0), ('account_id.reconcile', '=', True)]</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Amounts to settle
                </p><p>
                    Cool, it looks like you don't have any amount to settle.
                </p>
            </field>
        </record>

        <record id="action_move_out_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_invoice_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'out_receipt')]</field>
            <field name="context">{'default_move_type': 'out_receipt'}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Create a new sales receipt
              </p><p>
                When the sale receipt is confirmed, you can record the customer
                payment related to this sales receipt.
              </p>
            </field>
        </record>

        <record id="action_move_in_receipt_type" model="ir.actions.act_window">
            <field name="name">Receipts</field>
            <field name="res_model">account.move</field>
            <field name="view_mode">list,kanban,form,activity</field>
            <field name="view_id" ref="view_in_invoice_receipt_tree"/>
            <field name="search_view_id" ref="view_account_invoice_filter"/>
            <field name="domain">[('move_type', '=', 'in_receipt')]</field>
            <field name="context">{'default_move_type': 'in_receipt', 'display_account_trust': True}</field>
            <field name="help" type="html">
              <p class="o_view_nocontent_smiling_face">
                Register a new purchase receipt
              </p><p>
                When the purchase receipt is confirmed, you can record the
                vendor payment related to this purchase receipt.
              </p>
            </field>
        </record>

        <record id="action_move_line_form" model="ir.actions.act_window">
            <field name="name">Entries</field>
            <field name="res_model">account.move</field>
            <field name="view_id" ref="view_move_tree"/>
            <field name="search_view_id" ref="view_account_move_filter"/>
        </record>

        <record model="ir.actions.server" id="action_move_switch_move_type">
            <field name="name">Switch into invoice/credit note</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="state">code</field>
            <field name="binding_view_types">form</field>
            <field name="code">
if records:
    action = records.action_switch_move_type()
            </field>
        </record>

        <record model="ir.actions.server" id="action_move_force_register_payment">
            <field name="name">Pay</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="state">code</field>
            <field name="binding_view_types">form</field>
            <field name="code">
if records:
    action = records.action_force_register_payment()
            </field>
        </record>

        <record model="ir.actions.server" id="action_move_block_payment">
            <field name="name">(Un)Block Payment</field>
            <field name="model_id" ref="account.model_account_move"/>
            <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
            <field name="binding_model_id" ref="account.model_account_move" />
            <field name="state">code</field>
            <field name="binding_view_types">form</field>
            <field name="code">
                records.action_toggle_block_payment()
            </field>
        </record>

        <record model="ir.actions.server" id="action_check_hash_integrity">
            <field name="name">Data Inalterability Check</field>
            <field name="model_id" ref="account.model_res_company"/>
            <field name="state">code</field>
            <field name="code">
                action = env.company._action_check_hash_integrity()
            </field>
        </record>

    </data>
</odoo>

# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Uj<PERSON><PERSON>ak, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing error(s)"
msgstr "%(count)s इलेक्ट्रॉनिक इनवॉइसिंग में गड़बड़ी"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing info(s)"
msgstr "%(count)s इलेक्ट्रॉनिक इनवॉइसिंग की जानकारी"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing warning(s)"
msgstr "%(count)s इलेक्ट्रॉनिक इनवॉइसिंग से जुड़ी चेतावनी"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A cancellation of the EDI has been requested."
msgstr "ईडीआई को रद्द करेन का अनुरोध किया गया है."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A request for cancellation of the EDI has been called off."
msgstr "ईडीआई रद्द करने का अनुरोध वापस ले लिया गया है."

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_send
msgid "Account Move Send"
msgstr "अकाउंट मूव सेंड"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"Are you sure you want to cancel this invoice without waiting for the EDI "
"document to be canceled?"
msgstr ""
"क्या आप इनवॉइस को ईडीआई दस्तावेज़ के रद्द हुए बिना ही रद्द करना चाहते हैं?"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "अटैचमेंट"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "ब्लॉकिंग लेवल"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"यह संदेश बताता है कि दस्तावेज़ पर चल रहे काम पर, गलती कितनी गंभीर है, उसके हिसाब से क्या असर पड़ेगा:\n"
"* जानकारी: कोई दिक्कत नहीं है, दस्तावेज़ पर काम ठीक से हो रहा है.\n"
"* चेतावनी: कोई गलती है, लेकिन इससे इलेक्ट्रॉनिक इनवॉइस बनाने का काम रुकेगा नहीं.\n"
"* गड़बड़ी: कोई ऐसी गलती है जिससे इलेक्ट्रॉनिक इनवॉइस बनाने का काम पूरी तरह से रुक जाएगा."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "ईडीआई रद्द करने का अनुरोध वापस लें"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "निरस्त"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"इस जर्नल पर (%s) को बंद नहीं कर सकते, क्योंकि सभी दस्तावेज़ सिंक नहीं हुए "
"हैं"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "कोड"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "बदलाव किए जा सकने वाले ईडीआई"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "द्वारा निर्मित"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "इस तारीख को बनाया गया"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "डिस्प्ले नाम"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Download"
msgstr "डाउनलोड"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "EDI Documents"
msgstr "ईडीआई दस्तावेज़"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "ईडीआई फ़ॉर्मैट"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "ईडीआई फ़ॉर्मेट जो इस जर्नल की एंट्रीज़ के साथ काम करता है"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
msgid "EDI: Perform web services operations"
msgstr "ईडीआई: वेब सर्विस ऑपरेशन को परफ़ॉर्म करें"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
msgid "Edi Blocking Level"
msgstr "ईडीआई ब्लॉकिंग लेवल"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr "ईडीआई कॉन्टेंट"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
msgid "Edi Document"
msgstr "ईडीआई दस्तावेज"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
msgid "Edi Error Count"
msgstr "ईडीआई गड़बड़ी की संख्या"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
msgid "Edi Error Message"
msgstr "ईडीआई गड़बड़ी का मैसेज"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr "ईडीआई फ़ॉर्मैट"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr "ईडीआई"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr "ईडीआई में 'रद्द करें' बटन दिखाएं"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_force_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_force_cancel_button
msgid "Edi Show Force Cancel Button"
msgstr "ईडीआई में 'जबरन रद्द करें' बटन दिखाएं"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr "प्रोसेस करने के लिए ईडीआई वेब सर्विस"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr "account.move के लिए इलेक्ट्रॉनिक दस्तावेज़"

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
msgid "Electronic invoicing"
msgstr "इलेक्ट्रॉनिक इनवॉइसिंग"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr "इलेक्ट्रॉनिक इनवॉइस बनाने का काम करना है"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr "इलेक्ट्रॉनिक इनवॉइसिंग स्टेट"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "त्रुटि!"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Force Cancel"
msgstr "जबरदस्ती रद्द करें"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr "फॉर्मेट का नाम"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
msgid "How many EDIs are in error for this move?"
msgstr "इस लेन-देन के लिए कितने ईडीआई में गलती है?"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "आईडी"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "जानकारी"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""
"अमान्य इनवॉइस सेटिंग:\n"
"\n"
"%s"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "पत्रिका"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "जर्नल एंट्री"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "इन्होंने आखिरी बार अपडेट किया"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "आखिरी बार अपडेट हुआ"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "मूव"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "नाम"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr "एक लेन-देन और एक फ़ॉर्मेट के लिए एक ही ईडीआई दस्तावेज़ होगा"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Process now"
msgstr "अभी प्रोसेस करें"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "जर्नल एंट्रीज़ के सिक्वेंस को फिर से बनाएं"

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "रिपोर्ट ऐक्शन"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr "ईडीआई रद्द करने का अनुरोध करें"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Retry"
msgstr "दोबारा कोशिश करें"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "XML/EDI इनवॉइस भेजें"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "भेजा गया"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "स्थिति"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr ""
"इस लेन-देन के सभी ईडीआई की कुल स्थिति, खासकर जब वे ऑनलाइन सिस्टम से जुड़े "
"हों"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""
"यह फ़ाइल तब बनती है जब आप इनवॉइस को फाइनल करते हैं और यह काम edi_format_id "
"करता है (और यह दस्तावेज़ प्रोसेस किया जाता है)."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""
"जिन दस्तावेज़ों का उल्लेख किया गया है (%s), वे पहले ही भेजे जा चुके हैं, "
"इसलिए अब आप उनके क्रम को बदल नहीं सकते."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "The invoice will soon be sent to"
msgstr "जल्द ही इनवॉइस भेज दिया जाएगा"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""
"यह उस अंतिम गलती का विवरण है जो इलेक्ट्रॉनिक इनवॉइस का काम करते समय हुई थी."

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr "यह कोड पहले से ही मौजूद है"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
msgid "This document is being sent by another process already. "
msgstr "यह दस्तावेज़ अभी कोई और सिस्टम भेज रहा है."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"This invoice was canceled while the EDIs %s still had a pending cancellation"
" request."
msgstr ""
"यह इनवॉइस तब रद्द हुआ, जब ईडीआई %s को रद्द करने का अनुरोध अभी भी पेंडिंग था."

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr "रद्द करने के लिए"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "भेजने के लिए"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "चेतावनी"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""
"आप इस जर्नल एंट्री %s में बदलाव नहीं कर सकते, क्योंकि इसका इलेक्ट्रॉनिक "
"दस्तावेज़ पहले ही भेजा जा चुका है. अगर आप इसे रद्द करना चाहते हैं, तो 'ईडीआई"
" रद्द करने का अनुरोध करें' बटन का इस्तेमाल करें."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""
"आप उस अटैचमेंट को हटा नहीं सकते जो एक ईडीआई दस्तावेज़ है और जिसे सरकार को "
"भेजा जा चुका है."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "⇒ See errors"
msgstr "⇒ गड़बड़ियां देखें"

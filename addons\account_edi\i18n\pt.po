# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_edi
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2025
# NumerSpiral HBG, 2025
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing error(s)"
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing info(s)"
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "%(count)s Electronic invoicing warning(s)"
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A cancellation of the EDI has been requested."
msgstr "Foi solicitado o cancelamento do EDI."

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid "A request for cancellation of the EDI has been called off."
msgstr "Um pedido de cancelamento do EDI foi anulado."

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move_send
msgid "Account Move Send"
msgstr "Envio de Movimento de Conta"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid ""
"Are you sure you want to cancel this invoice without waiting for the EDI "
"document to be canceled?"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_attachment
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__attachment_id
msgid "Attachment"
msgstr "Anexo"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__blocking_level
msgid "Blocking Level"
msgstr "Nível de Bloqueio"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__blocking_level
msgid ""
"Blocks the current operation of the document depending on the error severity:\n"
"  * Info: the document is not blocked and everything is working as it should.\n"
"  * Warning: there is an error that doesn't prevent the current Electronic Invoicing operation to succeed.\n"
"  * Error: there is an error that blocks the current Electronic Invoicing operation."
msgstr ""
"Bloqueia a operação atual do documento dependendo da gravidade do erro:\n"
"* Info: o documento não está bloqueado e tudo está funcionando como deveria.\n"
"* Aviso: há um erro que não impede que a operação atual de Fatura Eletrónica seja bem-sucedida.\n"
"* Erro: há um erro que bloqueia a operação da Fatura Eletrónica atual."

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Call off EDI Cancellation"
msgstr "Anular cancelamento EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__cancelled
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__cancelled
msgid "Cancelled"
msgstr "Cancelada"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_journal.py:0
msgid ""
"Cannot deactivate (%s) on this journal because not all documents are "
"synchronized"
msgstr ""
"Não é possível desativar (%s) neste diário porque nem todos os documentos "
"estão sincronizados"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__code
msgid "Code"
msgstr "Código"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_journal__compatible_edi_ids
msgid "Compatible Edi"
msgstr "EDI Compatível"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__create_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__create_date
msgid "Created on"
msgstr "Criado em"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__display_name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Download"
msgstr "Transferir"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "EDI Documents"
msgstr "Documentos EDI"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_format
msgid "EDI format"
msgstr "FormatoEDI"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__compatible_edi_ids
msgid "EDI format that support moves in this journal"
msgstr "Formato EDI que suporta movimentos neste diário"

#. module: account_edi
#: model:ir.actions.server,name:account_edi.ir_cron_edi_network_ir_actions_server
msgid "EDI: Perform web services operations"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_blocking_level
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_blocking_level
msgid "Edi Blocking Level"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_content
msgid "Edi Content"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_document_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_document_ids
msgid "Edi Document"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_count
msgid "Edi Error Count"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_error_message
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_error_message
msgid "Edi Error Message"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_id
msgid "Edi Format"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_abandon_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_abandon_cancel_button
msgid "Edi Show Abandon Cancel Button"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_cancel_button
msgid "Edi Show Cancel Button"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_show_force_cancel_button
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_show_force_cancel_button
msgid "Edi Show Force Cancel Button"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_web_services_to_process
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_web_services_to_process
msgid "Edi Web Services To Process"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_edi_document
msgid "Electronic Document for an account.move"
msgstr ""

#. module: account_edi
#: model:ir.actions.act_window,name:account_edi.action_open_edi_documents
#: model:ir.model.fields,field_description:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,field_description:account_edi.field_account_journal__edi_format_ids
#: model:ir.model.fields,field_description:account_edi.field_account_move__edi_state
msgid "Electronic invoicing"
msgstr "Faturação Eletrónica"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing processing needed"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_account_invoice_filter
msgid "Electronic invoicing state"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__error
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__error
msgid "Error"
msgstr "Erro"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Force Cancel"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__edi_format_name
msgid "Format Name"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_error_count
#: model:ir.model.fields,help:account_edi.field_account_move__edi_error_count
msgid "How many EDIs are in error for this move?"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__id
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__id
msgid "ID"
msgstr "Id."

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__info
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__info
msgid "Info"
msgstr "Informação"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"Invalid invoice configuration:\n"
"\n"
"%s"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_journal
msgid "Journal"
msgstr "Diário"

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_move
msgid "Journal Entry"
msgstr "Lançamento em Diário"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_uid
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__write_date
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__move_id
msgid "Move"
msgstr "Mover"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__name
#: model:ir.model.fields,field_description:account_edi.field_account_edi_format__name
msgid "Name"
msgstr "Nome"

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_document_unique_edi_document_by_move_by_format
msgid "Only one edi document by move by format"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Process now"
msgstr ""

#. module: account_edi
#: model:ir.model,name:account_edi.model_account_resequence_wizard
msgid "Remake the sequence of Journal Entries."
msgstr "Refaça a sequência de lançamentos de diário."

#. module: account_edi
#: model:ir.model,name:account_edi.model_ir_actions_report
msgid "Report Action"
msgstr "Relatar Ação"

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Request EDI Cancellation"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "Retry"
msgstr "Tentar Novamente"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_journal__edi_format_ids
msgid "Send XML/EDI invoices"
msgstr "Enviar Faturas XML/EDI"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__sent
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__sent
msgid "Sent"
msgstr "Enviado"

#. module: account_edi
#: model:ir.model.fields,field_description:account_edi.field_account_edi_document__state
msgid "State"
msgstr "Estado"

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_bank_statement_line__edi_state
#: model:ir.model.fields,help:account_edi.field_account_move__edi_state
msgid "The aggregated state of all the EDIs with web-service of this move"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__attachment_id
msgid ""
"The file generated by edi_format_id when the invoice is posted (and this "
"document is processed)."
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/wizard/account_resequence.py:0
msgid ""
"The following documents have already been sent and cannot be resequenced: %s"
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "The invoice will soon be sent to"
msgstr ""

#. module: account_edi
#: model:ir.model.fields,help:account_edi.field_account_edi_document__error
msgid ""
"The text of the last error that happened during Electronic Invoice "
"operation."
msgstr ""

#. module: account_edi
#: model:ir.model.constraint,message:account_edi.constraint_account_edi_format_unique_code
msgid "This code already exists"
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_edi_document.py:0
msgid "This document is being sent by another process already. "
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"This invoice was canceled while the EDIs %s still had a pending cancellation"
" request."
msgstr ""

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_cancel
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_cancel
msgid "To Cancel"
msgstr ""

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__state__to_send
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_state__to_send
msgid "To Send"
msgstr "A enviar"

#. module: account_edi
#: model:ir.model.fields.selection,name:account_edi.selection__account_edi_document__blocking_level__warning
#: model:ir.model.fields.selection,name:account_edi.selection__account_move__edi_blocking_level__warning
msgid "Warning"
msgstr "Aviso"

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/account_move.py:0
msgid ""
"You can't edit the following journal entry %s because an electronic document"
" has already been sent. Please use the 'Request EDI Cancellation' button "
"instead."
msgstr ""

#. module: account_edi
#. odoo-python
#: code:addons/account_edi/models/ir_attachment.py:0
msgid ""
"You can't unlink an attachment being an EDI document sent to the government."
msgstr ""

#. module: account_edi
#: model_terms:ir.ui.view,arch_db:account_edi.view_move_form_inherit
msgid "⇒ See errors"
msgstr ""

# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_debit_note
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_move_form_debit
msgid "<span class=\"o_stat_text\">Debit Notes</span>"
msgstr "<span class=\"o_stat_text\">차변전표</span>"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr "차변전표 마법사 추가"

#. module: account_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Cancel"
msgstr "취소"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_journal__debit_sequence
msgid ""
"Check this box if you don't want to share the same sequence for invoices and"
" debit notes made from this journal"
msgstr "이 전표에서 생성된 청구서 및 직불 메모에 대해 동일한 순서를 공유하지 않으려면 이 확인란을 선택합니다."

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__copy_lines
msgid "Copy Lines"
msgstr "내용 복사"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__country_code
msgid "Country Code"
msgstr "국가 코드"

#. module: account_debit_note
#: model:ir.actions.act_window,name:account_debit_note.action_view_account_move_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Create Debit Note"
msgstr "차변전표 생성"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__create_date
msgid "Created on"
msgstr "작성일자"

#. module: account_debit_note
#: model:ir.actions.server,name:account_debit_note.action_move_debit_note
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_filter_debit
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_move_line_filter_debit
msgid "Debit Note"
msgstr "차변전표"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__date
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_debit_note
msgid "Debit Note Date"
msgstr "차변전표 날짜"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/models/account_move.py:0
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_ids
#: model_terms:ir.ui.view,arch_db:account_debit_note.view_account_invoice_filter_debit
msgid "Debit Notes"
msgstr "차변전표"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_journal__debit_sequence
msgid "Dedicated Debit Note Sequence"
msgstr "차변전표 자체 연번"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__display_name
msgid "Display Name"
msgstr "표시명"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__id
msgid "ID"
msgstr "ID"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__journal_id
msgid "If empty, uses the journal of the journal entry to be debited."
msgstr "비워둘 경우, 차변 분개할 전표를 사용합니다."

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__copy_lines
msgid ""
"In case you need to do corrections for every line, it can be in handy to "
"copy them.  We won't copy them for debit notes from credit notes. "
msgstr "전체 항목을 수정해야 할 경우, 복사하면 간편하게 사용할 수 있습니다. 차변전표에서 대변전표로는 복사할 수 없습니다."

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_journal
msgid "Journal"
msgstr "전표"

#. module: account_debit_note
#: model:ir.model,name:account_debit_note.model_account_move
msgid "Journal Entry"
msgstr "전표 입력"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_type
msgid "Journal Type"
msgstr "전표 유형"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_ids
msgid "Move"
msgstr "작업"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__move_type
msgid "Move Type"
msgstr "작업 유형"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_note_count
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_note_count
msgid "Number of Debit Notes"
msgstr "차변전표 수"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_bank_statement_line__debit_origin_id
#: model:ir.model.fields,field_description:account_debit_note.field_account_move__debit_origin_id
msgid "Original Invoice Debited"
msgstr "원래의 청구서 금액 출금"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__reason
msgid "Reason"
msgstr "사유"

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_debit_note__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"두 개의 문자로 이루어진 ISO 국가 코드입니다.\n"
"빠른 검색을 위해 사용합니다."

#. module: account_debit_note
#: model:ir.model.fields,help:account_debit_note.field_account_bank_statement_line__debit_note_ids
#: model:ir.model.fields,help:account_debit_note.field_account_move__debit_note_ids
msgid "The debit notes created for this invoice"
msgstr "이 청구서에 대해 생성된 차변전표"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "This debit note was created from: %s"
msgstr "이 차변전표는 다음에서 생성되었습니다: %s"

#. module: account_debit_note
#: model:ir.model.fields,field_description:account_debit_note.field_account_debit_note__journal_id
msgid "Use Specific Journal"
msgstr "특정 전표 사용"

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can make a debit note only for a Customer Invoice, a Customer Credit "
"Note, a Vendor Bill or a Vendor Credit Note."
msgstr "고객용 청구서/대변전표, 공급업체 청구서/대변전표에만 차변전표를 생성할 수 있습니다."

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid "You can only debit posted moves."
msgstr "승인된 전표만 차변 입력할 수 있습니다."

#. module: account_debit_note
#. odoo-python
#: code:addons/account_debit_note/wizard/account_debit_note.py:0
msgid ""
"You can't make a debit note for an invoice that is already linked to a debit"
" note."
msgstr "차변전표를 생성할 수 없습니다. 이미 청구서에 차변전표가 연결되어 있습니다."
